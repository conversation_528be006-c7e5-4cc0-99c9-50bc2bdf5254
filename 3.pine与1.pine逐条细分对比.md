# 3.pine与1.pine综合警报条件逐条细分对比

## 综合警报条件完整对比

### 3.pine综合警报条件
```pine
mrc_srbr_both_bullish = (current_mrc_signal == "做多" and current_srbr_signal == "看多" and price_above_mean_1h and price_above_r1_5m_check)
mrc_srbr_both_bearish = (current_mrc_signal == "做空" and current_srbr_signal == "看空" and price_below_mean_1h and price_below_s1_5m_check)
```

### 1.pine综合警报条件
```pine
mrc_srbr_both_bullish = (current_mrc_signal == "做多" and current_srbr_signal == "看多" and price_above_mean_1h and price_above_r1_5m_check and all_ma_distance_filters_pass and all_ma_range_filters_pass and all_ma50_above_filters_pass and all_ma200_above_filters_pass)
mrc_srbr_both_bearish = (current_mrc_signal == "做空" and current_srbr_signal == "看空" and price_below_mean_1h and price_below_s1_5m_check and all_ma_distance_filters_pass and all_ma_range_filters_pass and all_ma50_below_filters_pass and all_ma200_below_filters_pass)
```

## 逐条条件对比

### 条件1：MRC信号
#### 3.pine
```pine
current_mrc_signal == "做多"  // 做多信号
current_mrc_signal == "做空"  // 做空信号
```

#### 1.pine
```pine
current_mrc_signal == "做多"  // 做多信号
current_mrc_signal == "做空"  // 做空信号
```

**对比结果**：✅ 完全相同

---

### 条件2：SRBR信号
#### 3.pine
```pine
current_srbr_signal == "看多"  // 做多信号
current_srbr_signal == "看空"  // 做空信号
```

#### 1.pine
```pine
current_srbr_signal == "看多"  // 做多信号
current_srbr_signal == "看空"  // 做空信号
```

**对比结果**：✅ 完全相同

---

### 条件3：1小时MEAN线位置关系
#### 3.pine
```pine
price_above_mean_1h = mtf_1h and not na(close_1h) and not na(meanline_1h) ? (close_1h > meanline_1h) : true
price_below_mean_1h = mtf_1h and not na(close_1h) and not na(meanline_1h) ? (close_1h < meanline_1h) : true
```

#### 1.pine
```pine
price_above_mean_1h = mtf_1h and not na(close) and not na(meanline_1h) ? (close > meanline_1h) : true
price_below_mean_1h = mtf_1h and not na(close) and not na(meanline_1h) ? (close < meanline_1h) : true
```

**对比结果**：⚠️ 数据源不同
- **3.pine**：使用1小时收盘价 `close_1h`
- **1.pine**：使用实时价格 `close`

---

### 条件4：5分钟R1/S1线位置关系
#### 3.pine
```pine
price_above_r1_5m_check = mtf_5m and not na(close_5m) and not na(upband1_5m) ? (close_5m > upband1_5m) : true
price_below_s1_5m_check = mtf_5m and not na(close_5m) and not na(loband1_5m) ? (close_5m < loband1_5m) : true
```

#### 1.pine
```pine
price_above_r1_5m_check = mtf_5m and not na(close) and not na(upband1_5m) ? (close > upband1_5m) : true
price_below_s1_5m_check = mtf_5m and not na(close) and not na(loband1_5m) ? (close < loband1_5m) : true
```

**对比结果**：⚠️ 数据源不同
- **3.pine**：使用5分钟收盘价 `close_5m`
- **1.pine**：使用实时价格 `close`

---

### 条件5：MA200距离过滤
#### 3.pine
```pine
❌ 不存在此条件
```

#### 1.pine
```pine
all_ma_distance_filters_pass = true  // 暂时禁用，默认通过
```

**对比结果**：❌ 3.pine缺少此条件
- **3.pine**：无此过滤条件
- **1.pine**：有此条件但暂时禁用（默认通过）

---

### 条件6：MA50-MA200区间过滤
#### 3.pine
```pine
❌ 不存在此条件
```

#### 1.pine
```pine
all_ma_range_filters_pass = true  // 暂时禁用，默认通过
```

**对比结果**：❌ 3.pine缺少此条件
- **3.pine**：无此过滤条件
- **1.pine**：有此条件但暂时禁用（默认通过）

---

### 条件7：多时间周期MA50位置关系过滤
#### 3.pine
```pine
❌ 不存在此条件
```

#### 1.pine
```pine
all_ma50_above_filters_pass = price_above_ma50_5m and price_above_ma50_15m and price_above_ma50_1h
all_ma50_below_filters_pass = price_below_ma50_5m and price_below_ma50_15m and price_below_ma50_1h

其中：
price_above_ma50_5m = mtf_5m and not na(close) and not na(ma50_5m) ? (close > ma50_5m) : true
price_above_ma50_15m = mtf_15m and not na(close) and not na(ma50_15m) ? (close > ma50_15m) : true
price_above_ma50_1h = mtf_1h and not na(close) and not na(ma50_1h) ? (close > ma50_1h) : true
```

**对比结果**：❌ 3.pine缺少此条件
- **3.pine**：无此过滤条件
- **1.pine**：检查实时价格与5分钟、15分钟、1小时MA50的位置关系

---

### 条件8：多时间周期MA200位置关系过滤
#### 3.pine
```pine
❌ 不存在此条件
```

#### 1.pine
```pine
all_ma200_above_filters_pass = price_above_ma200_1m and price_above_ma200_5m and price_above_ma200_15m and price_above_ma200_1h and price_above_ma200_4h
all_ma200_below_filters_pass = price_below_ma200_1m and price_below_ma200_5m and price_below_ma200_15m and price_below_ma200_1h and price_below_ma200_4h

其中：
price_above_ma200_1m = mtf_1m and not na(close) and not na(ma200_1m) ? (close > ma200_1m) : true
price_above_ma200_5m = mtf_5m and not na(close) and not na(ma200_5m) ? (close > ma200_5m) : true
price_above_ma200_15m = mtf_15m and not na(close) and not na(ma200_15m) ? (close > ma200_15m) : true
price_above_ma200_1h = mtf_1h and not na(close) and not na(ma200_1h) ? (close > ma200_1h) : true
price_above_ma200_4h = enable_4h_ma200_filter and mtf_4h and not na(close) and not na(ma200_4h) ? (close > ma200_4h) : true
```

**对比结果**：❌ 3.pine缺少此条件
- **3.pine**：无此过滤条件
- **1.pine**：检查实时价格与1分钟、5分钟、15分钟、1小时、4小时MA200的位置关系

---

## 警报触发机制对比

### 3.pine警报条件
```pine
alertcondition(enable_combined_alerts and mrc_srbr_both_bullish and (alerts_only_on_close ? barstate.isconfirmed : true), title="综合做多信号", ...)
alertcondition(enable_combined_alerts and mrc_srbr_both_bearish and (alerts_only_on_close ? barstate.isconfirmed : true), title="综合做空信号", ...)
```

### 1.pine警报条件
```pine
combined_alert_condition_bullish = enable_combined_alerts and mrc_srbr_both_bullish and (not enable_volume_filter or long_volume_ok) and (alerts_only_on_close ? barstate.isconfirmed : true)
combined_alert_condition_bearish = enable_combined_alerts and mrc_srbr_both_bearish and (not enable_volume_filter or short_volume_ok) and (alerts_only_on_close ? barstate.isconfirmed : true)

alertcondition(combined_alert_condition_bullish, title="综合做多信号", ...)
alertcondition(combined_alert_condition_bearish, title="综合做空信号", ...)
```

**对比结果**：⚠️ 1.pine有额外的成交量过滤
- **3.pine**：只有收盘确认选项
- **1.pine**：有成交量过滤选项 + 收盘确认选项

---

## 条件数量统计

### 3.pine条件数量
- **基础条件**：4个
  1. MRC信号
  2. SRBR信号
  3. 1小时MEAN线位置关系
  4. 5分钟R1/S1线位置关系

- **警报额外条件**：1个
  5. 收盘确认（可选）

**总计**：5个条件（4个基础 + 1个可选）

### 1.pine条件数量
- **基础条件**：4个
  1. MRC信号
  2. SRBR信号
  3. 1小时MEAN线位置关系
  4. 5分钟R1/S1线位置关系

- **暂时禁用条件**：2个
  5. MA200距离过滤（默认通过）
  6. MA50-MA200区间过滤（默认通过）

- **MA50位置关系条件**：3个
  7. 实时价格 > 5分钟MA50
  8. 实时价格 > 15分钟MA50
  9. 实时价格 > 1小时MA50

- **MA200位置关系条件**：5个
  10. 实时价格 > 1分钟MA200
  11. 实时价格 > 5分钟MA200
  12. 实时价格 > 15分钟MA200
  13. 实时价格 > 1小时MA200
  14. 实时价格 > 4小时MA200（可控开关，默认关闭）

- **警报额外条件**：2个
  15. 成交量过滤（可选）
  16. 收盘确认（可选）

**总计**：16个条件（4个基础 + 2个禁用 + 8个MA位置 + 2个可选）

---

## 数据使用方式对比

### 价格数据源
#### 3.pine
- **1小时MEAN线检查**：使用 `close_1h`（1小时收盘价）
- **5分钟R1/S1线检查**：使用 `close_5m`（5分钟收盘价）

#### 1.pine
- **所有价格检查**：统一使用 `close`（实时价格）

### 指标数据获取
#### 3.pine和1.pine
- **共同特点**：都使用 `lookahead=barmerge.lookahead_off` 获取实时指标数据

**关键差异**：3.pine使用各时间周期的收盘价进行比较，1.pine使用实时价格进行比较

---

## 开关控制对比

### 3.pine可控制选项
1. `enable_combined_alerts`：是否启用综合警报
2. `alerts_only_on_close`：是否只在收盘时触发警报

### 1.pine可控制选项
1. `enable_combined_alerts`：是否启用综合警报
2. `enable_volume_filter`：是否启用成交量过滤
3. `alerts_only_on_close`：是否只在收盘时触发警报
4. `enable_4h_ma200_filter`：是否启用4小时MA200位置过滤

**对比结果**：1.pine提供更多的控制选项
