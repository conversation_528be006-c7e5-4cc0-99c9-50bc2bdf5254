// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// REUSING THIS CODE: You are welcome to reuse this code without permission, including in closed-source publications, as long as proper credits are given :)
// Author: ©fareidzulkifli

// Description : 
// Mean Reversion Channel objective, based on Mean Reversion theory (everything has a tendency to revert back to its mean), is to help visualizing: 
//     Inner Channel -> Dynamic Support and Resistance 
//     Outer Channel -> Overbought/Oversold Zone which may signal consolidation phase or potential reversal due to unsustainable move

// The concept of this indicator originally derived from Keltner Channel (The Keltner Channel was first introduced by <PERSON> in the 1960s. The original formula used simple moving averages (SMA) and the high-low price range to calculate the bands. In the 1980s, a new formula was introduced that used ATR.)
// Instead of using SMA/EMA, this indicator uses SuperSmoother MA as its mean with a longer lookback period (default to 200) to get a more stable channel line. I also added a second level so the indicator will have inner and outer channel.
// Details of each filtering type used for mean calculation can be read in Ehlers Technical Papers: "Swiss Army Knife Indicator" and/or his book "Cybernetics Analysis for Stock and Futures"

// Disclaimer:
// Past performance is not an indicator of future results.
// My opinions are my own and do not constitute financial advice in any way whatsoever. 
// Nothing published by me constitutes an investment/trading recommendation, nor should any data or Content published by me be relied upon for any investment/trading activities.
// I strongly recommend that you perform your own independent research and/or speak with a qualified investment professional before making any financial decisions.

// Any ideas to further improve this indicator are welcome :)

//@version=6
indicator('Mean Reversion Channel - 带价格标签', shorttitle = 'MRC2', overlay = true, format = format.inherit)

//************************************************************************************************************
// Parameter
//************************************************************************************************************

indiSet = input(false, '═════════ MRC Parameter ════════')
source = input(hlc3, title = 'Price Source')
type = input.string('SuperSmoother', title = 'Filter Type', options = ['SuperSmoother', 'Ehlers EMA', 'Gaussian', 'Butterworth', 'BandStop', 'SMA', 'EMA', 'RMA'])
length = input.int(200, title = 'Lookback Period', minval = 1)
innermult = input.float(1.0, title = 'Inner Channel Size Multiplier', minval = 0.1)
outermult = input.float(2.415, title = 'Outer Channel Size Multiplier', minval = 0.1)

ChartSet = input(false, '═════════ Chart Setting ════════')
drawchannel = input(true, title = 'Draw Channel')
displayzone = input(true, title = 'Draw Zone (With Channel)')
zonetransp = input.int(60, title = 'Zone Transparency', minval = 0, maxval = 100)
displayline = input(true, title = 'Display Line Extension')

CounterSet = input(false, '═════════ 碰撞计数设置 ════════')
enable_counter = input(true, title = '启用碰撞计数标记')
counter_tolerance = input.float(0.1, title = '碰撞容差百分比', minval = 0.01, maxval = 1.0, step = 0.01)
counter_size = input.string('Normal', title = '标记大小', options = ['Tiny', 'Small', 'Normal', 'Large', 'Huge'])
show_counter_table = input(true, title = '显示计数统计表')
show_connection_lines = input(true, title = '显示碰撞连接线')
max_line_count = input.int(50, title = '最大连接线数量', minval = 10, maxval = 200)

// 连接线设置
line_width = input.int(2, title = '连接线宽度', minval = 1, maxval = 10)
line_style_option = input.string('实线', title = '连接线样式', options = ['实线', '虚线', '点线'])
line_transparency = input.int(30, title = '连接线透明度', minval = 0, maxval = 100)
up_line_color = input.color(color.new(color.green, 30), title = '上升连接线颜色')
down_line_color = input.color(color.new(color.red, 30), title = '下降连接线颜色')
neutral_line_color = input.color(color.new(color.gray, 50), title = '中性连接线颜色')

MTFSet = input(false, '═════════ MTF Setting ════════')
enable_mtf = input(true, title = 'Enable Multiple TimeFrame Analysis')
mtf_disp_typ = input.string('On Hover', title = 'MTF Display Type', options = ['Always Display', 'On Hover'])
mtf_typ = input.string('Auto', title = 'Multiple TimeFrame Type', options = ['Auto', 'Custom'])
mtf_lvl1 = input.timeframe('D', title = 'Custom MTF Level 1')
mtf_lvl2 = input.timeframe('W', title = 'Custom MTF Level 2')

//************************************************************************************************************
// Functions Start {
//************************************************************************************************************
var pi = 2 * math.asin(1)
var mult = pi * innermult
var mult2 = pi * outermult
var gradsize = 0.5
var gradtransp = zonetransp

//-----------------------
// Ehler SwissArmyKnife Function
//-----------------------
SAK_smoothing(_type, _src, _length) =>
    c0 = 1.0
    c1 = 0.0
    b0 = 1.0
    b1 = 0.0
    b2 = 0.0
    a1 = 0.0
    a2 = 0.0
    alpha = 0.0
    beta = 0.0
    gamma = 0.0
    cycle = 2 * pi / _length

    if _type == 'Ehlers EMA'
        alpha := (math.cos(cycle) + math.sin(cycle) - 1) / math.cos(cycle)
        b0 := alpha
        a1 := 1 - alpha
        a1
    if _type == 'Gaussian'
        beta := 2.415 * (1 - math.cos(cycle))
        alpha := -beta + math.sqrt(beta * beta + 2 * beta)
        c0 := alpha * alpha
        a1 := 2 * (1 - alpha)
        a2 := -(1 - alpha) * (1 - alpha)
        a2
    if _type == 'Butterworth'
        beta := 2.415 * (1 - math.cos(cycle))
        alpha := -beta + math.sqrt(beta * beta + 2 * beta)
        c0 := alpha * alpha / 4
        b1 := 2
        b2 := 1
        a1 := 2 * (1 - alpha)
        a2 := -(1 - alpha) * (1 - alpha)
        a2
    if _type == 'BandStop'
        beta := math.cos(cycle)
        gamma := 1 / math.cos(cycle * 2 * 0.1) // delta default to 0.1. Acceptable delta -- 0.05<d<0.5
        alpha := gamma - math.sqrt(gamma * gamma - 1)
        c0 := (1 + alpha) / 2
        b1 := -2 * beta
        b2 := 1
        a1 := beta * (1 + alpha)
        a2 := -alpha
        a2
    if _type == 'SMA'
        c1 := 1 / _length
        b0 := 1 / _length
        a1 := 1
        a1
    if _type == 'EMA'
        alpha := 2 / (_length + 1)
        b0 := alpha
        a1 := 1 - alpha
        a1
    if _type == 'RMA'
        alpha := 1 / _length
        b0 := alpha
        a1 := 1 - alpha
        a1

    _Input = _src
    _Output = 0.0
    _Output := c0 * (b0 * _Input + b1 * nz(_Input[1]) + b2 * nz(_Input[2])) + a1 * nz(_Output[1]) + a2 * nz(_Output[2]) - c1 * nz(_Input[_length])
    _Output

//-----------------------
// SuperSmoother Function
//-----------------------
supersmoother(_src, _length) =>
    s_a1 = math.exp(-math.sqrt(2) * pi / _length)
    s_b1 = 2 * s_a1 * math.cos(math.sqrt(2) * pi / _length)
    s_c3 = -math.pow(s_a1, 2)
    s_c2 = s_b1
    s_c1 = 1 - s_c2 - s_c3
    ss = 0.0
    ss := s_c1 * _src + s_c2 * nz(ss[1], _src[1]) + s_c3 * nz(ss[2], _src[2])
    ss

//-----------------------
// Auto TimeFrame Function
//-----------------------
// ————— Converts current chart resolution into a float minutes value.
f_resInMinutes() =>
    _resInMinutes = timeframe.multiplier * (timeframe.isseconds ? 1. / 60 : timeframe.isminutes ? 1. : timeframe.isdaily ? 60. * 24 : timeframe.isweekly ? 60. * 24 * 7 : timeframe.ismonthly ? 60. * 24 * 30.4375 : na)
    _resInMinutes

get_tf(_lvl) =>
    y = f_resInMinutes()
    z = timeframe.period
    if mtf_typ == 'Auto'
        if y < 1
            z := _lvl == 1 ? '1' : _lvl == 2 ? '5' : z
            z
        else if y <= 3
            z := _lvl == 1 ? '5' : _lvl == 2 ? '15' : z
            z
        else if y <= 10
            z := _lvl == 1 ? '15' : _lvl == 2 ? '60' : z
            z
        else if y <= 30
            z := _lvl == 1 ? '60' : _lvl == 2 ? '240' : z
            z
        else if y <= 120
            z := _lvl == 1 ? '240' : _lvl == 2 ? 'D' : z
            z
        else if y <= 240
            z := _lvl == 1 ? 'D' : _lvl == 2 ? 'W' : z
            z
        else if y <= 1440
            z := _lvl == 1 ? 'W' : _lvl == 2 ? 'M' : z
            z
        else if y <= 10080
            z := _lvl == 1 ? 'M' : z
            z
        else
            z := z
            z
    else
        z := _lvl == 1 ? mtf_lvl1 : _lvl == 2 ? mtf_lvl2 : z
        z

    z

//-----------------------
// Mean Reversion Channel Function
//-----------------------
get_mrc() =>
    v_condition = 0
    v_meanline = source
    v_meanrange = supersmoother(ta.tr, length)

    //-- Get Line value
    if type == 'SuperSmoother'
        v_meanline := supersmoother(source, length)
        v_meanline

    if type != 'SuperSmoother'
        v_meanline := SAK_smoothing(type, source, length)
        v_meanline

    v_upband1 = v_meanline + v_meanrange * mult
    v_loband1 = v_meanline - v_meanrange * mult
    v_upband2 = v_meanline + v_meanrange * mult2
    v_loband2 = v_meanline - v_meanrange * mult2

    //-- Check Condition
    if close > v_meanline
        v_upband2_1 = v_upband2 + v_meanrange * gradsize * 4
        v_upband2_9 = v_upband2 + v_meanrange * gradsize * -4
        if high >= v_upband2_9 and high < v_upband2
            v_condition := 1
            v_condition
        else if high >= v_upband2 and high < v_upband2_1
            v_condition := 2
            v_condition
        else if high >= v_upband2_1
            v_condition := 3
            v_condition
        else if close <= v_meanline + v_meanrange
            v_condition := 4
            v_condition
        else
            v_condition := 5
            v_condition

    if close < v_meanline
        v_loband2_1 = v_loband2 - v_meanrange * gradsize * 4
        v_loband2_9 = v_loband2 - v_meanrange * gradsize * -4
        if low <= v_loband2_9 and low > v_loband2
            v_condition := -1
            v_condition
        else if low <= v_loband2 and low > v_loband2_1
            v_condition := -2
            v_condition
        else if low <= v_loband2_1
            v_condition := -3
            v_condition
        else if close >= v_meanline + v_meanrange
            v_condition := -4
            v_condition
        else
            v_condition := -5
            v_condition

    [v_meanline, v_meanrange, v_upband1, v_loband1, v_upband2, v_loband2, v_condition]

//-----------------------
// MTF Analysis
//-----------------------

get_stat(_cond) =>
    ret = 'Price at Mean Line\n'
    if _cond == 1
        ret := 'Overbought (Weak)\n'
        ret
    else if _cond == 2
        ret := 'Overbought\n'
        ret
    else if _cond == 3
        ret := 'Overbought (Strong)\n'
        ret
    else if _cond == 4
        ret := 'Price Near Mean\n'
        ret
    else if _cond == 5
        ret := 'Price Above Mean\n'
        ret
    else if _cond == -1
        ret := 'Oversold (Weak)\n'
        ret
    else if _cond == -2
        ret := 'Oversold\n'
        ret
    else if _cond == -3
        ret := 'Oversold (Strong)\n'
        ret
    else if _cond == -4
        ret := 'Price Near Mean\n'
        ret
    else if _cond == -5
        ret := 'Price Below Mean\n'
        ret
    ret

//-----------------------
// Chart Drawing Function
//-----------------------
format_price(x) =>
    y = str.tostring(x, '0.00000')
    if x > 10
        y := str.tostring(x, '0.000')
        y
    if x > 1000
        y := str.tostring(x, '0.00')
        y
    y

f_PriceLine(_ref, linecol) =>
    line.new(x1 = bar_index, x2 = bar_index - 1, y1 = _ref, y2 = _ref, extend = extend.left, color = linecol)

f_MTFLabel(_txt, _yloc) =>
    label.new(x = time + math.round(ta.change(time) * 20), y = _yloc, xloc = xloc.bar_time, text = mtf_disp_typ == 'Always Display' ? _txt : 'Check MTF', tooltip = mtf_disp_typ == 'Always Display' ? '' : _txt, color = color.black, textcolor = color.white, size = size.normal, style = mtf_disp_typ == 'On Hover' and displayline ? label.style_label_lower_left : label.style_label_left, textalign = text.align_left)

//} Function End

//************************************************************************************************************
// Calculate Channel
//************************************************************************************************************
var tf_0 = timeframe.period
var tf_1 = get_tf(1)
var tf_2 = get_tf(2)

[meanline, meanrange, upband1, loband1, upband2, loband2, condition] = get_mrc()
[mtf1_meanline, mtf1_meanrange, mtf1_upband1, mtf1_loband1, mtf1_upband2, mtf1_loband2, mtf1_condition] = request.security(syminfo.tickerid, tf_1, get_mrc())
[mtf2_meanline, mtf2_meanrange, mtf2_upband1, mtf2_loband1, mtf2_upband2, mtf2_loband2, mtf2_condition] = request.security(syminfo.tickerid, tf_2, get_mrc())

//************************************************************************************************************
// 碰撞计数逻辑
//************************************************************************************************************

// 计算容差范围
tolerance_pct = counter_tolerance / 100
mean_tolerance = meanline * tolerance_pct
r1_tolerance = upband1 * tolerance_pct
s1_tolerance = loband1 * tolerance_pct

// 判断价格是否在关键位置附近 - 使用更精确的检测
near_mean = math.abs(close - meanline) <= mean_tolerance
near_r1 = math.abs(close - upband1) <= r1_tolerance
near_s1 = math.abs(close - loband1) <= s1_tolerance

// 检测真正的碰撞 - 价格穿越或触及线条
hit_mean = (high >= meanline - mean_tolerance and low <= meanline + mean_tolerance)
hit_r1 = (high >= upband1 - r1_tolerance and low <= upband1 + r1_tolerance)
hit_s1 = (high >= loband1 - s1_tolerance and low <= loband1 + s1_tolerance)

// 价格位置状态
var int price_state = 0  // 0: MEAN, 1: R1区域, -1: S1区域, 2: 其他上方, -2: 其他下方
var int collision_counter = 0
var int last_collision_bar = 0
var string last_collision_type = ""

// 确定当前价格状态
current_state = 0
if near_mean
    current_state := 0
else if near_r1
    current_state := 1
else if near_s1
    current_state := -1
else if close > meanline
    current_state := 2
else
    current_state := -2

// 检测实际碰撞事件 - 更精确的检测
collision_detected = false
collision_line_price = 0.0
collision_type_detected = ""

// 检测从非接触状态到接触状态的转变
if hit_mean and current_state == 0 and price_state[1] != 0
    collision_detected := true
    collision_line_price := meanline
    collision_type_detected := "MEAN"
else if hit_r1 and current_state == 1 and price_state[1] != 1
    collision_detected := true
    collision_line_price := upband1
    collision_type_detected := "R1"
else if hit_s1 and current_state == -1 and price_state[1] != -1
    collision_detected := true
    collision_line_price := loband1
    collision_type_detected := "S1"

// 碰撞检测和计数逻辑
var bool state_changed = false
state_changed := current_state != price_state[1] and enable_counter

// 防止在同一根K线上重复计数
bar_gap = bar_index - last_collision_bar
min_bar_gap = 2  // 至少间隔2根K线，避免震荡

// 记录上一次的计数状态，用于判断趋势
var int last_meaningful_counter = 0

// 连接线管理
var line[] connection_lines = array.new<line>()
var int last_label_bar = 0
var float last_label_price = 0.0
var float collision_price = 0.0  // 记录碰撞时的实际价格
var bool just_collided = false   // 标记是否刚刚发生碰撞

// 重置碰撞标志
just_collided := false

// 使用新的碰撞检测逻辑
if collision_detected and bar_gap >= min_bar_gap
    just_collided := true  // 设置碰撞标志

    // 到达MEAN位置 - 重置为0
    if collision_type_detected == "MEAN"
        collision_counter := 0
        last_collision_bar := bar_index
        last_collision_type := "MEAN"
        last_meaningful_counter := 0
        collision_price := collision_line_price  // 使用线条价格

    // 到达R1位置
    else if collision_type_detected == "R1"
        // 如果之前在MEAN或者是正数计数，则递增
        if price_state[1] == 0 or last_meaningful_counter >= 0
            collision_counter := last_meaningful_counter + 1
        else
            // 从负数区域来到R1，重新开始计数
            collision_counter := 1
        last_collision_bar := bar_index
        last_collision_type := "R1"
        last_meaningful_counter := collision_counter
        collision_price := collision_line_price  // 使用线条价格

    // 到达S1位置
    else if collision_type_detected == "S1"
        // 如果之前在MEAN或者是负数计数，则递减
        if price_state[1] == 0 or last_meaningful_counter <= 0
            collision_counter := last_meaningful_counter - 1
        else
            // 从正数区域来到S1，重新开始计数
            collision_counter := -1
        last_collision_bar := bar_index
        last_collision_type := "S1"
        last_meaningful_counter := collision_counter
        collision_price := collision_line_price  // 使用线条价格

// 更新价格状态
price_state := current_state

//************************************************************************************************************
// Drawing Start {
//************************************************************************************************************
float p_meanline = drawchannel ? meanline : na
float p_upband1 = drawchannel ? upband1 : na
float p_loband1 = drawchannel ? loband1 : na
float p_upband2 = drawchannel ? upband2 : na
float p_loband2 = drawchannel ? loband2 : na

z = plot(p_meanline, color = color.new(color.black, 0), style = plot.style_line, title = ' Mean', linewidth = 2)
x1 = plot(p_upband1, color = color.new(color.black, 0), style = plot.style_line, title = ' R1', linewidth = 2)
x2 = plot(p_loband1, color = color.new(color.black, 0), style = plot.style_line, title = ' S1', linewidth = 2)
y1 = plot(p_upband2, color = color.new(color.red, 50), style = plot.style_line, title = ' R2', linewidth = 1)
y2 = plot(p_loband2, color = color.new(color.red, 50), style = plot.style_line, title = ' S2', linewidth = 1)

//-----------------------
// Draw zone
//-----------------------
//---
var color1 = #FF0000
var color2 = #FF4200
var color3 = #FF5D00
var color4 = #FF7400
var color5 = #FF9700
var color6 = #FFAE00
var color7 = #FFC500
var color8 = #FFCD00
//---
float upband2_1 = drawchannel and displayzone ? upband2 + meanrange * gradsize * 4 : na
float loband2_1 = drawchannel and displayzone ? loband2 - meanrange * gradsize * 4 : na
float upband2_2 = drawchannel and displayzone ? upband2 + meanrange * gradsize * 3 : na
float loband2_2 = drawchannel and displayzone ? loband2 - meanrange * gradsize * 3 : na
float upband2_3 = drawchannel and displayzone ? upband2 + meanrange * gradsize * 2 : na
float loband2_3 = drawchannel and displayzone ? loband2 - meanrange * gradsize * 2 : na
float upband2_4 = drawchannel and displayzone ? upband2 + meanrange * gradsize * 1 : na
float loband2_4 = drawchannel and displayzone ? loband2 - meanrange * gradsize * 1 : na
float upband2_5 = drawchannel and displayzone ? upband2 + meanrange * gradsize * 0 : na
float loband2_5 = drawchannel and displayzone ? loband2 - meanrange * gradsize * 0 : na
float upband2_6 = drawchannel and displayzone ? upband2 + meanrange * gradsize * -1 : na
float loband2_6 = drawchannel and displayzone ? loband2 - meanrange * gradsize * -1 : na
float upband2_7 = drawchannel and displayzone ? upband2 + meanrange * gradsize * -2 : na
float loband2_7 = drawchannel and displayzone ? loband2 - meanrange * gradsize * -2 : na
float upband2_8 = drawchannel and displayzone ? upband2 + meanrange * gradsize * -3 : na
float loband2_8 = drawchannel and displayzone ? loband2 - meanrange * gradsize * -3 : na
float upband2_9 = drawchannel and displayzone ? upband2 + meanrange * gradsize * -4 : na
float loband2_9 = drawchannel and displayzone ? loband2 - meanrange * gradsize * -4 : na

//---
plot_upband2_1 = plot(upband2_1, color = na, display = display.none)
plot_loband2_1 = plot(loband2_1, color = na, display = display.none)
plot_upband2_2 = plot(upband2_2, color = na, display = display.none)
plot_loband2_2 = plot(loband2_2, color = na, display = display.none)
plot_upband2_3 = plot(upband2_3, color = na, display = display.none)
plot_loband2_3 = plot(loband2_3, color = na, display = display.none)
plot_upband2_4 = plot(upband2_4, color = na, display = display.none)
plot_loband2_4 = plot(loband2_4, color = na, display = display.none)
plot_upband2_5 = plot(upband2_5, color = na, display = display.none)
plot_loband2_5 = plot(loband2_5, color = na, display = display.none)
plot_upband2_6 = plot(upband2_6, color = na, display = display.none)
plot_loband2_6 = plot(loband2_6, color = na, display = display.none)
plot_upband2_7 = plot(upband2_7, color = na, display = display.none)
plot_loband2_7 = plot(loband2_7, color = na, display = display.none)
plot_upband2_8 = plot(upband2_8, color = na, display = display.none)
plot_loband2_8 = plot(loband2_8, color = na, display = display.none)
plot_upband2_9 = plot(upband2_9, color = na, display = display.none)
plot_loband2_9 = plot(loband2_9, color = na, display = display.none)

//---
fill(plot_upband2_1, plot_upband2_2, color = color1)
fill(plot_loband2_1, plot_loband2_2, color = color1)
fill(plot_upband2_2, plot_upband2_3, color = color2)
fill(plot_loband2_2, plot_loband2_3, color = color2)
fill(plot_upband2_3, plot_upband2_4, color = color3)
fill(plot_loband2_3, plot_loband2_4, color = color3)
fill(plot_upband2_4, plot_upband2_5, color = color4)
fill(plot_loband2_4, plot_loband2_5, color = color4)
fill(plot_upband2_5, plot_upband2_6, color = color5)
fill(plot_loband2_5, plot_loband2_6, color = color5)
fill(plot_upband2_6, plot_upband2_7, color = color6)
fill(plot_loband2_6, plot_loband2_7, color = color6)
fill(plot_upband2_7, plot_upband2_8, color = color7)
fill(plot_loband2_7, plot_loband2_8, color = color7)
fill(plot_upband2_8, plot_upband2_9, color = color8)
fill(plot_loband2_8, plot_loband2_9, color = color8)

//-----------------------
// Plot Extension
//-----------------------
if displayline and enable_mtf and mtf_disp_typ == 'Always Display'
    displayline := false
    displayline

var line mean = na
line.delete(mean)
mean := displayline ? f_PriceLine(meanline, #FFCD00) : na
var line res1 = na
line.delete(res1)
res1 := displayline ? f_PriceLine(upband1, color.green) : na
var line sup1 = na
line.delete(sup1)
sup1 := displayline ? f_PriceLine(loband1, color.green) : na
var line res2 = na
line.delete(res2)
res2 := displayline ? f_PriceLine(upband2, color.red) : na
var line sup2 = na
line.delete(sup2)
sup2 := displayline ? f_PriceLine(loband2, color.red) : na

//--------------
// Prep MTF Label
//--------------
var brl = '\n--------------------------------------'
dist_0 = 'Distance from Mean: ' + str.tostring((close - meanline) / close * 100, '#.##') + ' %'
dist_1 = 'Distance from Mean: ' + str.tostring((close - mtf1_meanline) / close * 100, '#.##') + ' %'
dist_2 = 'Distance from Mean: ' + str.tostring((close - mtf2_meanline) / close * 100, '#.##') + ' %'

var title = 'Mean Reversion Channel\nMultiple TimeFrame Analysis' + brl
tf0 = '\n\nTimeframe: ' + tf_0 + ' (Current)\n\nStatus: ' + get_stat(condition) + dist_0 + brl

tf1 = not timeframe.ismonthly ? '\n\nTimeframe: ' + tf_1 + '\n\nStatus: ' + get_stat(mtf1_condition) + dist_1 + brl : ''

tf2 = not timeframe.isweekly and not timeframe.ismonthly ? '\n\nTimeframe: ' + tf_2 + '\n\nStatus: ' + get_stat(mtf2_condition) + dist_2 + brl : ''

mtf_lbl = title + tf0 + tf1 + tf2
var label label_mtf = na
label.delete(label_mtf)
label_mtf := enable_mtf ? f_MTFLabel(mtf_lbl, meanline) : na

//************************************************************************************************************
// 碰撞计数标记显示
//************************************************************************************************************

// 获取标记大小
get_label_size() =>
    switch counter_size
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
        => size.normal

// 获取连接线样式
get_line_style() =>
    switch line_style_option
        "实线" => line.style_solid
        "虚线" => line.style_dashed
        "点线" => line.style_dotted
        => line.style_solid

// 显示碰撞计数标记
if enable_counter and just_collided
    label_color = color.white
    text_color = color.black
    label_style = label.style_label_center

    // 根据计数值和位置确定颜色和样式
    if collision_counter > 0
        label_color := collision_counter == 1 ? color.new(color.lime, 0) : collision_counter == 2 ? color.new(color.blue, 0) : collision_counter == 3 ? color.new(color.purple, 0) : collision_counter >= 4 ? color.new(color.orange, 0) : color.new(color.green, 0)
        text_color := color.white
        label_style := current_state == 1 ? label.style_label_down : label.style_label_up
    else if collision_counter < 0
        label_color := collision_counter == -1 ? color.new(color.red, 0) : collision_counter == -2 ? color.new(color.maroon, 0) : collision_counter == -3 ? color.new(color.fuchsia, 0) : collision_counter <= -4 ? color.new(color.navy, 0) : color.new(color.red, 0)
        text_color := color.white
        label_style := current_state == -1 ? label.style_label_up : label.style_label_down
    else  // collision_counter == 0
        label_color := color.new(color.yellow, 0)
        text_color := color.black
        label_style := label.style_label_center

    // 确定标记位置 - 使用线条价格确保精确对齐
    label_y = collision_price

    // 微调标记位置以避免与线条重叠
    label_offset = (high - low) * 0.1  // 使用当前K线高低差的10%作为偏移
    if last_collision_type == "R1"
        label_y := collision_price + label_offset
    else if last_collision_type == "S1"
        label_y := collision_price - label_offset

    // 创建标记文本
    label_text = collision_counter == 0 ? "0" : str.tostring(collision_counter)

    // 创建标记 - 确保固定在K线上
    new_label = label.new(x = bar_index, y = label_y, text = label_text, xloc = xloc.bar_index, yloc = yloc.price, color = label_color, textcolor = text_color, size = get_label_size(), style = label_style, tooltip = "碰撞计数: " + str.tostring(collision_counter) + "\n位置: " + last_collision_type + "\n价格: " + str.tostring(close, "#.####"))

    // 绘制连接线
    if show_connection_lines and last_label_bar > 0
        // 确定连接线颜色 - 使用用户设置的颜色
        line_color = color.white
        if collision_counter > last_meaningful_counter
            line_color := up_line_color
        else if collision_counter < last_meaningful_counter
            line_color := down_line_color
        else
            line_color := neutral_line_color

        // 创建连接线 - 使用用户设置
        connection_line = line.new(x1 = last_label_bar, y1 = last_label_price, x2 = bar_index, y2 = label_y, xloc = xloc.bar_index, color = line_color, width = line_width, style = get_line_style())

        // 管理连接线数量
        array.push(connection_lines, connection_line)
        if array.size(connection_lines) > max_line_count
            old_line = array.shift(connection_lines)
            line.delete(old_line)

    // 更新最后标记位置
    last_label_bar := bar_index
    last_label_price := collision_price  // 使用实际碰撞价格

//************************************************************************************************************
// 碰撞统计表
//************************************************************************************************************

// 统计各种碰撞次数
var int total_mean_hits = 0
var int total_r1_hits = 0
var int total_s1_hits = 0
var int max_positive_count = 0
var int max_negative_count = 0

// 更新统计数据
if enable_counter and state_changed and bar_gap >= min_bar_gap
    if last_collision_type == "MEAN"
        total_mean_hits += 1
    else if last_collision_type == "R1"
        total_r1_hits += 1
        max_positive_count := math.max(max_positive_count, collision_counter)
    else if last_collision_type == "S1"
        total_s1_hits += 1
        max_negative_count := math.max(max_negative_count, math.abs(collision_counter))

// 显示统计表
if show_counter_table and enable_counter and barstate.islast
    var table stats_table = table.new(position.top_right, 2, 7, bgcolor = color.new(color.white, 80), border_width = 1)

    table.cell(stats_table, 0, 0, "碰撞统计", text_color = color.black, text_size = size.normal, bgcolor = color.new(color.gray, 50))
    table.cell(stats_table, 1, 0, "次数", text_color = color.black, text_size = size.normal, bgcolor = color.new(color.gray, 50))

    table.cell(stats_table, 0, 1, "MEAN碰撞", text_color = color.black, text_size = size.small)
    table.cell(stats_table, 1, 1, str.tostring(total_mean_hits), text_color = color.black, text_size = size.small)

    table.cell(stats_table, 0, 2, "R1碰撞", text_color = color.black, text_size = size.small)
    table.cell(stats_table, 1, 2, str.tostring(total_r1_hits), text_color = color.black, text_size = size.small)

    table.cell(stats_table, 0, 3, "S1碰撞", text_color = color.black, text_size = size.small)
    table.cell(stats_table, 1, 3, str.tostring(total_s1_hits), text_color = color.black, text_size = size.small)

    table.cell(stats_table, 0, 4, "最大正计数", text_color = color.black, text_size = size.small)
    table.cell(stats_table, 1, 4, str.tostring(max_positive_count), text_color = color.black, text_size = size.small)

    table.cell(stats_table, 0, 5, "最大负计数", text_color = color.black, text_size = size.small)
    table.cell(stats_table, 1, 5, str.tostring(max_negative_count), text_color = color.black, text_size = size.small)

    table.cell(stats_table, 0, 6, "当前计数", text_color = color.black, text_size = size.small)
    table.cell(stats_table, 1, 6, str.tostring(collision_counter), text_color = collision_counter > 0 ? color.green : collision_counter < 0 ? color.red : color.orange, text_size = size.small)

//************************************************************************************************************
// Real-time Price Labels for Extended Lines (Placed on the right side of the last candle)
//************************************************************************************************************

// Define persistent label variables so that they update rather than create new labels each bar
var label mean_label = na
var label res1_label = na
var label sup1_label = na
var label res2_label = na
var label sup2_label = na
var label res2_9_label = na  // Label for upband2_9
var label sup2_9_label = na  // Label for loband2_9
var label res2_1_label = na  // New label for upband2_1
var label sup2_1_label = na  // New label for loband2_1

if displayline and barstate.islast
    // MEAN label
    if na(mean_label)
        mean_label := label.new(bar_index, meanline, "MEAN: " + format_price(meanline), xloc = xloc.bar_index, style = label.style_label_left, color = color.new(color.black, 0), textcolor = color.white)
    else
        label.set_xy(mean_label, bar_index, meanline)
        label.set_text(mean_label, "MEAN: " + format_price(meanline))
    // R1 label
    if na(res1_label)
        res1_label := label.new(bar_index, upband1, "R1: " + format_price(upband1), xloc = xloc.bar_index, style = label.style_label_left, color = color.new(color.green, 0), textcolor = color.white)
    else
        label.set_xy(res1_label, bar_index, upband1)
        label.set_text(res1_label, "R1: " + format_price(upband1))
    // S1 label
    if na(sup1_label)
        sup1_label := label.new(bar_index, loband1, "S1: " + format_price(loband1), xloc = xloc.bar_index, style = label.style_label_left, color = color.new(color.green, 0), textcolor = color.white)
    else
        label.set_xy(sup1_label, bar_index, loband1)
        label.set_text(sup1_label, "S1: " + format_price(loband1))
    // R2 label
    if na(res2_label)
        res2_label := label.new(bar_index, upband2, "R2: " + format_price(upband2), xloc = xloc.bar_index, style = label.style_label_left, color = color.new(color.red, 0), textcolor = color.white)
    else
        label.set_xy(res2_label, bar_index, upband2)
        label.set_text(res2_label, "R2: " + format_price(upband2))
    // S2 label
    if na(sup2_label)
        sup2_label := label.new(bar_index, loband2, "S2: " + format_price(loband2), xloc = xloc.bar_index, style = label.style_label_left, color = color.new(color.red, 0), textcolor = color.white)
    else
        label.set_xy(sup2_label, bar_index, loband2)
        label.set_text(sup2_label, "S2: " + format_price(loband2))
    // R2_9 label (for upband2_9)
    if na(res2_9_label)
        res2_9_label := label.new(bar_index, upband2_9, "R2_9: " + format_price(upband2_9), xloc = xloc.bar_index, style = label.style_label_left, color = color.new(color.red, 50), textcolor = color.white)
    else
        label.set_xy(res2_9_label, bar_index, upband2_9)
        label.set_text(res2_9_label, "R2_9: " + format_price(upband2_9))
    // S2_9 label (for loband2_9)
    if na(sup2_9_label)
        sup2_9_label := label.new(bar_index, loband2_9, "S2_9: " + format_price(loband2_9), xloc = xloc.bar_index, style = label.style_label_left, color = color.new(color.red, 50), textcolor = color.white)
    else
        label.set_xy(sup2_9_label, bar_index, loband2_9)
        label.set_text(sup2_9_label, "S2_9: " + format_price(loband2_9))
    // R2_1 label (New for upband2_1)
    if na(res2_1_label)
        res2_1_label := label.new(bar_index, upband2_1, "R2_1: " + format_price(upband2_1), xloc = xloc.bar_index, style = label.style_label_left, color = color.new(color.red, 50), textcolor = color.white)
    else
        label.set_xy(res2_1_label, bar_index, upband2_1)
        label.set_text(res2_1_label, "R2_1: " + format_price(upband2_1))
    // S2_1 label (New for loband2_1)
    if na(sup2_1_label)
        sup2_1_label := label.new(bar_index, loband2_1, "S2_1: " + format_price(loband2_1), xloc = xloc.bar_index, style = label.style_label_left, color = color.new(color.red, 50), textcolor = color.white)
    else
        label.set_xy(sup2_1_label, bar_index, loband2_1)
        label.set_text(sup2_1_label, "S2_1: " + format_price(loband2_1))
//************************************************************************************************************
// Alerts: 当价格触碰 MEAN、R1、S1、R2、S2 时报警（保留原始 message 模板）
//************************************************************************************************************
// 碰到 MEAN 警报
alertcondition(ta.cross(close, meanline), title="碰到 MEAN", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"碰到MEAN","位置":"MEAN"}')

// 碰到 R1 警报
alertcondition(ta.cross(close, upband1), title="碰到 R1", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"碰到R1","位置":"R1"}')

// 碰到 S1 警报
alertcondition(ta.cross(close, loband1), title="碰到 S1", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"碰到S1","位置":"S1"}')

// 碰到 R2 警报
alertcondition(ta.cross(close, upband2), title="碰到 R2", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"碰到R2","位置":"R2"}')

// 碰到 S2 警报
alertcondition(ta.cross(close, loband2), title="碰到 S2", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"碰到S2","位置":"S2"}')

// 价格处于 MEAN 和 R1 之间
alertcondition(close > meanline and close < upband1, title="价格处于 MEAN 和 R1 之间", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于MEAN和R1之间","位置":"MEAN-R1"}')

// 价格处于 MEAN 和 S1 之间
alertcondition(close < meanline and close > loband1, title="价格处于 MEAN 和 S1 之间", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于MEAN和S1之间","位置":"MEAN-S1"}')

// 价格处于 R1 和 R2 之间
alertcondition(close > upband1 and close < upband2, title="价格处于 R1 和 R2 之间", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于R1和R2之间","位置":"R1-R2"}')

// 价格处于 S1 和 S2 之间
alertcondition(close < loband1 and close > loband2, title="价格处于 S1 和 S2 之间", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于S1和S2之间","位置":"S1-S2"}')

// 价格处于 R1 和 R2_9 之间
alertcondition(close > upband1 and close < upband2_9, title="价格处于 R1 和 R2_9 之间", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于R1和R2_9之间","位置":"R1-R2_9"}')

// 价格处于 R2_9 和 R2_1 之间
alertcondition(close > upband2_9 and close < upband2_1, title="价格处于 R2_9 和 R2_1 之间", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于R2_9和R2_1之间","位置":"R2_9-R2_1"}')

// 价格处于 S1 和 S2_9 之间
alertcondition(close < loband1 and close > loband2_9, title="价格处于 S1 和 S2_9 之间", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于S1和S2_9之间","位置":"S1-S2_9"}')

// 价格处于 S2_9 和 S2_1 之间
alertcondition(close < loband2_9 and close > loband2_1, title="价格处于 S2_9 和 S2_1 之间", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于S2_9和S2_1之间","位置":"S2_9-S2_1"}')

// 价格上穿 R1 警报
alertcondition(ta.crossover(close, upband1), title="价格上穿 R1", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格上穿R1","位置":"R1"}')

// 价格下穿 R1 警报
alertcondition(ta.crossunder(close, upband1), title="价格下穿 R1", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格下穿R1","位置":"R1"}')

// 价格下穿 S1 警报
alertcondition(ta.crossunder(close, loband1), title="价格下穿 S1", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格下穿S1","位置":"S1"}')

// 价格上穿 S1 警报
alertcondition(ta.crossover(close, loband1), title="价格上穿 S1", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格上穿S1","位置":"S1"}')

// 价格上穿 MEAN 警报
alertcondition(ta.crossover(close, meanline), title="价格上穿 MEAN", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格上穿MEAN","位置":"MEAN"}')

// 价格下穿 MEAN 警报
alertcondition(ta.crossunder(close, meanline), title="价格下穿 MEAN", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格下穿MEAN","位置":"MEAN"}')

// 价格上穿 S2_9 警报
alertcondition(ta.crossover(close, loband2_9), title="价格上穿 S2_9", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格上穿S2_9","位置":"S2_9"}')

// 价格下穿 S2_9 警报
alertcondition(ta.crossunder(close, loband2_9), title="价格下穿 S2_9", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格下穿S2_9","位置":"S2_9"}')

// 价格上穿 R2_9 警报
alertcondition(ta.crossover(close, upband2_9), title="价格上穿 R2_9", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格上穿R2_9","位置":"R2_9"}')

// 价格下穿 R2_9 警报
alertcondition(ta.crossunder(close, upband2_9), title="价格下穿 R2_9", message='{"指标名称":"MRC","交易对":"{{ticker}}","周期":"{{interval}}","MEAN":"{{plot_0}}","R1":"{{plot_1}}","S1":"{{plot_2}}","R2":"{{plot_3}}","S2":"{{plot_4}}","R2_9":"{{plot_19}}","S2_9":"{{plot_20}}","R2_1":"{{plot_5}}","S2_1":"{{plot_6}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格下穿R2_9","位置":"R2_9"}')