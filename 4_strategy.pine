//@version=6
strategy('MRC+MA200+RSI+srbr8.1 Strategy', shorttitle='MRC Strategy', overlay=true, max_labels_count=500, default_qty_type=strategy.percent_of_equity, default_qty_value=10, initial_capital=10000, currency=currency.USD, commission_type=strategy.commission.percent, commission_value=0.1)

//************************************************************************************************************
// 策略参数设置区域
//************************************************************************************************************

// ═════════ 策略交易配置 ════════
strategySet = input(false, '═════════ Strategy Trading Configuration ════════')
enable_strategy = input.bool(true, title='启用策略交易', group='Strategy')
signal_hold_minutes = input.int(3, title='信号持有时间（分钟）', minval=1, maxval=30, group='Strategy', tooltip='如果在此时间内没有新的同方向信号，则平仓')
enable_json_alerts = input.bool(true, title='启用JSON格式交易通知', group='Strategy')
position_size_percent = input.float(10.0, title='仓位大小（%）', minval=1.0, maxval=100.0, step=1.0, group='Strategy')
enable_stop_loss = input.bool(false, title='启用止损', group='Strategy')
stop_loss_percent = input.float(2.0, title='止损百分比（%）', minval=0.1, maxval=10.0, step=0.1, group='Strategy')
enable_take_profit = input.bool(false, title='启用止盈', group='Strategy')
take_profit_percent = input.float(4.0, title='止盈百分比（%）', minval=0.1, maxval=20.0, step=0.1, group='Strategy')
max_concurrent_positions = input.int(1, title='最大并发持仓数', minval=1, maxval=5, group='Strategy')
enable_reverse_signals = input.bool(true, title='启用反向信号平仓', group='Strategy', tooltip='当出现反向信号时立即平仓')

// ═════════ 信号检测配置 ════════
signalSet = input(false, '═════════ Signal Detection Configuration ════════')
use_1m_signals = input.bool(true, title='使用1分钟综合信号', group='Signals', tooltip='基于1分钟时间框架的MRC+SRBR综合信号')
require_volume_confirmation = input.bool(true, title='要求成交量确认', group='Signals')
min_signal_strength = input.string('强', title='最小信号强度', options=['强', '弱', '冲突'], group='Signals')

// ═════════ 风险管理配置 ════════
riskSet = input(false, '═════════ Risk Management Configuration ════════')
max_daily_trades = input.int(10, title='每日最大交易次数', minval=1, maxval=50, group='Risk')
max_daily_loss_percent = input.float(5.0, title='每日最大亏损（%）', minval=0.1, maxval=20.0, step=0.1, group='Risk')
enable_time_filter = input.bool(false, title='启用时间过滤', group='Risk')
start_hour = input.int(9, title='交易开始时间（小时）', minval=0, maxval=23, group='Risk')
end_hour = input.int(17, title='交易结束时间（小时）', minval=0, maxval=23, group='Risk')

//************************************************************************************************************
// 从原始指标导入核心计算函数（简化版本）
//************************************************************************************************************

// ═════════ MRC 核心计算 ═════════
// SuperSmoother 函数
supersmoother(src, len) =>
    pi = 2 * math.asin(1)
    s_a1 = math.exp(-math.sqrt(2) * pi / len)
    s_b1 = 2 * s_a1 * math.cos(math.sqrt(2) * pi / len)
    s_c3 = -math.pow(s_a1, 2)
    s_c2 = s_b1
    s_c1 = 1 - s_c2 - s_c3
    ss = 0.0
    ss := s_c1 * src + s_c2 * nz(ss[1], src[1]) + s_c3 * nz(ss[2], src[2])
    ss

// MRC 计算函数（简化版）
get_mrc_simple() =>
    source = hlc3
    length = 200
    innermult = 1.0
    
    v_meanline = supersmoother(source, length)
    v_meanrange = supersmoother(ta.tr, length)
    
    pi = 2 * math.asin(1)
    mult = pi * innermult
    
    upband1 = v_meanline + v_meanrange * mult
    loband1 = v_meanline - v_meanrange * mult
    
    [v_meanline, upband1, loband1]

// ═════════ RSI 计算 ═════════
get_rsi_simple() =>
    rsi = ta.rsi(close, 14)
    
    // 简化的动态阈值
    var rsi_values = array.new_float(0)
    if last_bar_index - bar_index <= 100
        array.push(rsi_values, rsi)
    
    long_threshold = 70.0
    short_threshold = 30.0
    
    if array.size(rsi_values) > 10
        long_threshold := array.percentile_linear_interpolation(rsi_values, 75)
        short_threshold := array.percentile_linear_interpolation(rsi_values, 25)
    
    [rsi, long_threshold, short_threshold]

// ═════════ SRBR 计算（简化版） ═════════
get_srbr_simple() =>
    lookback = 18
    
    // 简化的成交量分析
    Vol = close > open ? volume : close < open ? -volume : 0.0
    vol_hi = ta.highest(Vol / 2.5, 5)
    vol_lo = ta.lowest(Vol / 2.5, 5)
    
    var float supportLevel = na
    var float resistanceLevel = na
    
    pivotHigh = ta.pivothigh(close, lookback, lookback)
    pivotLow = ta.pivotlow(close, lookback, lookback)
    
    if not na(pivotLow) and Vol > vol_hi
        supportLevel := pivotLow
    
    if not na(pivotHigh) and Vol < vol_lo
        resistanceLevel := pivotHigh
    
    [supportLevel, resistanceLevel]

//************************************************************************************************************
// 策略状态管理
//************************************************************************************************************

// ═════════ 策略状态变量 ═════════
var int last_signal_time = na
var string last_signal_direction = na
var bool position_opened = false
var float entry_price = na
var int entry_time = na
var string current_position_direction = na
var int daily_trade_count = 0
var float daily_pnl = 0.0
var int last_trade_day = na

//************************************************************************************************************
// 核心策略逻辑
//************************************************************************************************************

// ═════════ 计算指标数值 ═════════
[meanline, upband1, loband1] = get_mrc_simple()
[rsi, long_threshold, short_threshold] = get_rsi_simple()
[supportLevel, resistanceLevel] = get_srbr_simple()

// ═════════ 信号判断逻辑 ═════════
// MRC 信号
rsi_overbought = rsi > long_threshold
rsi_oversold = rsi < short_threshold
price_above_r1 = close > upband1
price_below_s1 = close < loband1
ma50 = ta.ema(close, 50)
ma200 = ta.ema(close, 200)
ma50_above_mean = ma50 > meanline
ma50_below_mean = ma50 < meanline
ma200_distance = math.abs(close - ma200)
ma200_far = ma200_distance > 3.0  // 简化的距离阈值

mrc_long = rsi_overbought and price_above_r1 and ma200_far and ma50_above_mean
mrc_short = rsi_oversold and price_below_s1 and ma200_far and ma50_below_mean

// SRBR 信号
srbr_bullish = not na(supportLevel) and not na(resistanceLevel) and close > resistanceLevel
srbr_bearish = not na(supportLevel) and not na(resistanceLevel) and close < supportLevel

// 综合信号
combined_long = mrc_long and srbr_bullish
combined_short = mrc_short and srbr_bearish

//************************************************************************************************************
// 策略交易逻辑
//************************************************************************************************************

// ═════════ 辅助函数 ═════════
// 检查是否超过信号持有时间
is_signal_expired() =>
    if na(last_signal_time)
        false
    else
        time_diff = (time - last_signal_time) / 60000  // 转换为分钟
        time_diff > signal_hold_minutes

// 检查是否可以开新仓
can_open_position() =>
    if not enable_strategy
        false
    else
        // 检查并发持仓数
        current_positions = strategy.position_size != 0 ? 1 : 0
        positions_ok = current_positions < max_concurrent_positions

        // 检查每日交易次数
        current_day = dayofweek(time)
        if na(last_trade_day) or last_trade_day != current_day
            daily_trade_count := 0
            daily_pnl := 0.0
            last_trade_day := current_day

        trades_ok = daily_trade_count < max_daily_trades

        // 检查每日亏损限制
        loss_ok = daily_pnl > -max_daily_loss_percent

        // 检查时间过滤
        time_ok = true
        if enable_time_filter
            current_hour = hour(time)
            time_ok := current_hour >= start_hour and current_hour <= end_hour

        positions_ok and trades_ok and loss_ok and time_ok

// 生成JSON格式的交易通知
generate_json_alert(action, direction, price, reason) =>
    json_msg = '{"action":"' + action + '","direction":"' + direction + '","symbol":"' + syminfo.ticker + '","timeframe":"' + timeframe.period + '","price":' + str.tostring(price, '#.####') + ',"time":"' + str.format_time(time, "yyyy-MM-dd HH:mm:ss") + '","reason":"' + reason + '","position_size":' + str.tostring(position_size_percent) + ',"strategy":"MRC+MA200+RSI+SRBR"}'
    json_msg

// ═════════ 主策略逻辑 ═════════
if enable_strategy
    // 获取1分钟信号（如果启用）
    signal_long = use_1m_signals ? request.security(syminfo.tickerid, "1", combined_long, lookahead=barmerge.lookahead_off) : combined_long
    signal_short = use_1m_signals ? request.security(syminfo.tickerid, "1", combined_short, lookahead=barmerge.lookahead_off) : combined_short

    // 检查是否有新信号
    has_long_signal = signal_long and not signal_long[1]  // 新的做多信号
    has_short_signal = signal_short and not signal_short[1]  // 新的做空信号

    // 信号过期检查
    signal_expired = is_signal_expired()

    // 反向信号检查
    reverse_signal = false
    if not na(current_position_direction)
        reverse_signal := (current_position_direction == "LONG" and has_short_signal) or (current_position_direction == "SHORT" and has_long_signal)

    // ═════════ 开仓逻辑 ═════════
    if can_open_position() and not position_opened
        if has_long_signal
            qty = position_size_percent / 100 * strategy.equity / close
            strategy.entry("Long", strategy.long, qty=qty, comment="综合做多信号")
            position_opened := true
            entry_price := close
            entry_time := time
            current_position_direction := "LONG"
            last_signal_time := time
            last_signal_direction := "LONG"
            daily_trade_count := daily_trade_count + 1

            // JSON通知
            if enable_json_alerts
                alert(generate_json_alert("OPEN", "LONG", close, "综合做多信号触发"), alert.freq_once_per_bar)

        else if has_short_signal
            qty = position_size_percent / 100 * strategy.equity / close
            strategy.entry("Short", strategy.short, qty=qty, comment="综合做空信号")
            position_opened := true
            entry_price := close
            entry_time := time
            current_position_direction := "SHORT"
            last_signal_time := time
            last_signal_direction := "SHORT"
            daily_trade_count := daily_trade_count + 1

            // JSON通知
            if enable_json_alerts
                alert(generate_json_alert("OPEN", "SHORT", close, "综合做空信号触发"), alert.freq_once_per_bar)

    // ═════════ 信号延续逻辑 ═════════
    // 如果有新的同方向信号，更新最后信号时间
    if position_opened and not na(last_signal_direction)
        if (last_signal_direction == "LONG" and signal_long) or (last_signal_direction == "SHORT" and signal_short)
            last_signal_time := time

            // JSON通知 - 信号延续
            if enable_json_alerts
                alert(generate_json_alert("HOLD", last_signal_direction, close, "同方向信号延续"), alert.freq_once_per_bar)

    // ═════════ 平仓逻辑 ═════════
    close_position = false
    close_reason = ""

    // 1. 信号过期平仓
    if position_opened and signal_expired
        close_position := true
        close_reason := "信号过期(" + str.tostring(signal_hold_minutes) + "分钟无新信号)"

    // 2. 反向信号平仓
    if position_opened and enable_reverse_signals and reverse_signal
        close_position := true
        close_reason := "反向信号触发"

    // 3. 止损平仓
    if position_opened and enable_stop_loss and not na(entry_price)
        if current_position_direction == "LONG" and close <= entry_price * (1 - stop_loss_percent/100)
            close_position := true
            close_reason := "止损触发(-" + str.tostring(stop_loss_percent) + "%)"
        else if current_position_direction == "SHORT" and close >= entry_price * (1 + stop_loss_percent/100)
            close_position := true
            close_reason := "止损触发(-" + str.tostring(stop_loss_percent) + "%)"

    // 4. 止盈平仓
    if position_opened and enable_take_profit and not na(entry_price)
        if current_position_direction == "LONG" and close >= entry_price * (1 + take_profit_percent/100)
            close_position := true
            close_reason := "止盈触发(+" + str.tostring(take_profit_percent) + "%)"
        else if current_position_direction == "SHORT" and close <= entry_price * (1 - take_profit_percent/100)
            close_position := true
            close_reason := "止盈触发(+" + str.tostring(take_profit_percent) + "%)"

    // 执行平仓
    if close_position
        strategy.close_all(comment=close_reason)

        // 计算本次交易盈亏
        if not na(entry_price)
            trade_pnl = (close - entry_price) / entry_price * 100 * (current_position_direction == "LONG" ? 1 : -1)
            daily_pnl := daily_pnl + trade_pnl

        // JSON通知
        if enable_json_alerts
            alert(generate_json_alert("CLOSE", current_position_direction, close, close_reason), alert.freq_once_per_bar)

        // 重置状态
        position_opened := false
        entry_price := na
        entry_time := na
        current_position_direction := na
        last_signal_time := na
        last_signal_direction := na

//************************************************************************************************************
// 策略状态显示
//************************************************************************************************************

// ═════════ 策略状态表格 ═════════
if enable_strategy and barstate.islast
    // 创建策略状态表格
    var table strategy_table = table.new(
         position = position.top_left,
         columns = 2,
         rows = 10,
         bgcolor = color.new(color.white, 85),
         border_width = 1)

    // 清空表格
    table.clear(strategy_table, 0, 0, 1, 9)

    // 策略状态标题
    table.cell(strategy_table, 0, 0, "策略状态", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=size.small)
    table.cell(strategy_table, 1, 0, "实时信息", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=size.small)

    // 策略启用状态
    table.cell(strategy_table, 0, 1, "策略状态", text_color=color.black, text_size=size.tiny)
    strategy_status_text = enable_strategy ? "✓ 已启用" : "✗ 已禁用"
    strategy_status_color = enable_strategy ? color.new(color.green, 60) : color.new(color.red, 60)
    table.cell(strategy_table, 1, 1, strategy_status_text, text_color=color.black, bgcolor=strategy_status_color, text_size=size.tiny)

    // 当前持仓状态
    table.cell(strategy_table, 0, 2, "持仓状态", text_color=color.black, text_size=size.tiny)
    position_status_text = position_opened ? "✓ 持仓中" : "✗ 无持仓"
    position_status_color = position_opened ? color.new(color.lime, 60) : color.new(color.gray, 60)
    table.cell(strategy_table, 1, 2, position_status_text, text_color=color.black, bgcolor=position_status_color, text_size=size.tiny)

    // 持仓方向
    table.cell(strategy_table, 0, 3, "持仓方向", text_color=color.black, text_size=size.tiny)
    direction_text = position_opened ? current_position_direction : "无"
    direction_color = position_opened ? (current_position_direction == "LONG" ? color.new(color.green, 60) : color.new(color.red, 60)) : color.new(color.gray, 60)
    table.cell(strategy_table, 1, 3, direction_text, text_color=color.black, bgcolor=direction_color, text_size=size.tiny)

    // 入场价格
    table.cell(strategy_table, 0, 4, "入场价格", text_color=color.black, text_size=size.tiny)
    entry_text = position_opened and not na(entry_price) ? str.tostring(entry_price, '#.####') : "无"
    table.cell(strategy_table, 1, 4, entry_text, text_color=color.black, text_size=size.tiny)

    // 当前盈亏
    table.cell(strategy_table, 0, 5, "当前盈亏", text_color=color.black, text_size=size.tiny)
    if position_opened and not na(entry_price)
        pnl_percent = (close - entry_price) / entry_price * 100 * (current_position_direction == "LONG" ? 1 : -1)
        pnl_text = (pnl_percent >= 0 ? "+" : "") + str.tostring(pnl_percent, '#.2') + "%"
        pnl_color = pnl_percent >= 0 ? color.new(color.green, 60) : color.new(color.red, 60)
    else
        pnl_text = "无"
        pnl_color = color.new(color.gray, 60)
    table.cell(strategy_table, 1, 5, pnl_text, text_color=color.black, bgcolor=pnl_color, text_size=size.tiny)

    // 信号剩余时间
    table.cell(strategy_table, 0, 6, "信号剩余", text_color=color.black, text_size=size.tiny)
    if position_opened and not na(last_signal_time)
        time_left = math.max(0, signal_hold_minutes - (time - last_signal_time) / 60000)
        time_text = str.tostring(time_left, '#.1') + " 分钟"
        time_color = time_left > 1 ? color.new(color.green, 60) : color.new(color.orange, 60)
    else
        time_text = "无"
        time_color = color.new(color.gray, 60)
    table.cell(strategy_table, 1, 6, time_text, text_color=color.black, bgcolor=time_color, text_size=size.tiny)

    // 当前信号状态
    table.cell(strategy_table, 0, 7, "当前信号", text_color=color.black, text_size=size.tiny)
    signal_text = combined_long ? "做多" : combined_short ? "做空" : "无信号"
    signal_color = combined_long ? color.new(color.green, 60) : combined_short ? color.new(color.red, 60) : color.new(color.gray, 60)
    table.cell(strategy_table, 1, 7, signal_text, text_color=color.black, bgcolor=signal_color, text_size=size.tiny)

    // 每日统计
    table.cell(strategy_table, 0, 8, "今日交易", text_color=color.black, text_size=size.tiny)
    daily_text = str.tostring(daily_trade_count) + "/" + str.tostring(max_daily_trades)
    daily_color = daily_trade_count >= max_daily_trades ? color.new(color.red, 60) : color.new(color.green, 60)
    table.cell(strategy_table, 1, 8, daily_text, text_color=color.black, bgcolor=daily_color, text_size=size.tiny)

    // 每日盈亏
    table.cell(strategy_table, 0, 9, "今日盈亏", text_color=color.black, text_size=size.tiny)
    daily_pnl_text = (daily_pnl >= 0 ? "+" : "") + str.tostring(daily_pnl, '#.2') + "%"
    daily_pnl_color = daily_pnl >= 0 ? color.new(color.green, 60) : color.new(color.red, 60)
    table.cell(strategy_table, 1, 9, daily_pnl_text, text_color=color.black, bgcolor=daily_pnl_color, text_size=size.tiny)

//************************************************************************************************************
// 警报条件
//************************************************************************************************************

// ═════════ 策略交易警报 ═════════
// 开仓警报
alertcondition(enable_strategy and position_opened and barstate.isconfirmed, title="策略开仓", message='{"action":"OPEN","strategy":"MRC+MA200+RSI+SRBR","symbol":"' + syminfo.ticker + '","timeframe":"' + timeframe.period + '","price":"' + str.tostring(close, '#.####') + '","direction":"' + current_position_direction + '","position_size":"' + str.tostring(position_size_percent) + '%","time":"' + str.format_time(time, "yyyy-MM-dd HH:mm:ss") + '","reason":"综合信号触发"}')

// 平仓警报
alertcondition(enable_strategy and not position_opened and barstate.isconfirmed, title="策略平仓", message='{"action":"CLOSE","strategy":"MRC+MA200+RSI+SRBR","symbol":"' + syminfo.ticker + '","timeframe":"' + timeframe.period + '","price":"' + str.tostring(close, '#.####') + '","time":"' + str.format_time(time, "yyyy-MM-dd HH:mm:ss") + '","reason":"策略平仓条件触发"}')

// 信号延续警报
alertcondition(enable_strategy and position_opened and not na(last_signal_direction), title="信号延续", message='{"action":"HOLD","strategy":"MRC+MA200+RSI+SRBR","symbol":"' + syminfo.ticker + '","timeframe":"' + timeframe.period + '","price":"' + str.tostring(close, '#.####') + '","direction":"' + last_signal_direction + '","time":"' + str.format_time(time, "yyyy-MM-dd HH:mm:ss") + '","reason":"同方向信号延续"}')

// 风险警报
alertcondition(enable_strategy and daily_trade_count >= max_daily_trades, title="每日交易次数达到上限", message='{"action":"RISK_ALERT","strategy":"MRC+MA200+RSI+SRBR","symbol":"' + syminfo.ticker + '","reason":"每日交易次数达到上限","count":"' + str.tostring(daily_trade_count) + '","limit":"' + str.tostring(max_daily_trades) + '"}')

alertcondition(enable_strategy and daily_pnl <= -max_daily_loss_percent, title="每日亏损达到上限", message='{"action":"RISK_ALERT","strategy":"MRC+MA200+RSI+SRBR","symbol":"' + syminfo.ticker + '","reason":"每日亏损达到上限","pnl":"' + str.tostring(daily_pnl, '#.2') + '%","limit":"' + str.tostring(max_daily_loss_percent, '#.2') + '%"}')

//************************************************************************************************************
// 图表标记
//************************************************************************************************************

// ═════════ 信号标记 ═════════
// 综合做多信号标记
plotshape(combined_long and enable_strategy, title="综合做多信号", style=shape.triangleup, location=location.belowbar, color=color.lime, size=size.normal)

// 综合做空信号标记
plotshape(combined_short and enable_strategy, title="综合做空信号", style=shape.triangledown, location=location.abovebar, color=color.red, size=size.normal)

// 开仓标记
plotshape(position_opened and barstate.isconfirmed, title="开仓标记", style=shape.circle, location=location.absolute, color=color.yellow, size=size.small)

// 平仓标记
plotshape(not position_opened and barstate.isconfirmed and not na(entry_price[1]), title="平仓标记", style=shape.xcross, location=location.absolute, color=color.orange, size=size.small)

// ═════════ 关键价位线 ═════════
// 绘制MRC通道
plot(meanline, title="MEAN线", color=color.blue, linewidth=2)
plot(upband1, title="R1阻力", color=color.red, linewidth=1)
plot(loband1, title="S1支撑", color=color.green, linewidth=1)

// 绘制MA线
plot(ma50, title="MA50", color=color.orange, linewidth=1)
plot(ma200, title="MA200", color=color.purple, linewidth=2)
