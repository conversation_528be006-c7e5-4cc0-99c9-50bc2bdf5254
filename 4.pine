//@version=6
strategy('MRC+MA200+RSI+srbr8.1 Strategy', shorttitle='MRC+MA200+srbr8.1 Strategy', overlay=true, max_labels_count=500, default_qty_type=strategy.percent_of_equity, default_qty_value=10, initial_capital=10000, currency=currency.USD, commission_type=strategy.commission.percent, commission_value=0.1)

//************************************************************************************************************
// 参数设置区域
//************************************************************************************************************

// ═════════ MRC 参数 ════════
mrcSet = input(false, '═════════ MRC Parameter ════════')
source = input(hlc3, title='Price Source', group='MRC')
type = input.string('SuperSmoother', title='Filter Type', options=['SuperSmoother', 'Ehlers EMA', 'Gaussian', 'Butterworth', 'BandStop', 'SMA', 'EMA', 'RMA'], group='MRC')
length = input.int(200, title='Lookback Period', minval=1, group='MRC')
innermult = input.float(1.0, title='Inner Channel Size Multiplier', minval=0.1, group='MRC')
outermult = input.float(2.415, title='Outer Channel Size Multiplier', minval=0.1, group='MRC')

// ═════════ MA200 参数 ════════
maSet = input(false, '═════════ MA200 Parameter ════════')
exponential = input.bool(true, title='使用指数移动平均线 (EMA)', group='MA200')
show_ma_lines = input.bool(true, title='显示MA线条', group='MA200')

// ═════════ MA200距离判定参数（参考原MA200代码） ════════
distanceSet = input(false, '═════════ MA200 Distance Analysis Parameter ════════')
default_dist = input.float(3.0, title='默认 MA200 距离阈值', minval=0.1, step=0.1, group='Distance')
btc_dist = input.float(300.0, title='BTCUSD MA200 距离阈值', minval=0.1, step=0.1, group='Distance')
xau_dist = input.float(3.5, title='XAUUSD MA200 距离阈值', minval=0.1, step=0.1, group='Distance')
eth_dist = input.float(9.0, title='ETHUSD MA200 距离阈值', minval=0.1, step=0.1, group='Distance')
gbp_dist = input.float(0.3, title='GBPJPY MA200 距离阈值', minval=0.1, step=0.1, group='Distance')

flatness_period = input.int(20, title='MA200 波动回看周期', minval=1, group='Distance')
flatness_threshold = input.float(0.5, title='MA200 平坦度阈值', minval=0.01, step=0.01, group='Distance')
oscillation_bars = input.int(10, title='价格波动回看周期', minval=1, group='Distance')
oscillation_threshold = input.float(5.0, title='价格波动范围阈值', minval=0.1, step=0.1, group='Distance')

// ═════════ R1/S1距离判定参数 ════════
r1s1DistanceSet = input(false, '═════════ R1/S1 Distance Analysis Parameter ════════')
default_r1s1_dist = input.float(3.0, title='默认 R1/S1 距离阈值', minval=0.1, step=0.1, group='R1S1Distance')
btc_r1s1_dist = input.float(300.0, title='BTCUSD R1/S1 距离阈值', minval=0.1, step=0.1, group='R1S1Distance')
xau_r1s1_dist = input.float(3.5, title='XAUUSD R1/S1 距离阈值', minval=0.1, step=0.1, group='R1S1Distance')
eth_r1s1_dist = input.float(9.0, title='ETHUSD R1/S1 距离阈值', minval=0.1, step=0.1, group='R1S1Distance')
gbp_r1s1_dist = input.float(0.3, title='GBPJPY R1/S1 距离阈值', minval=0.1, step=0.1, group='R1S1Distance')

// ═════════ K线标记配置 ════════
signalSet = input(false, '═════════ Signal Marks Configuration ════════')
show_long_marks = input.bool(true, title='显示做多标记', group='Signals')
show_short_marks = input.bool(true, title='显示做空标记', group='Signals')
show_oscillation_marks = input.bool(false, title='显示震荡标记', group='Signals')
show_advanced_alerts = input.bool(true, title='显示高级警报标记', group='Signals')
advanced_alert_style = input.string('超级突出', title='高级警报样式', options=['普通', '突出', '超级突出'], group='Signals')
arrow_size = input.string('small', title='图形标记大小', options=['tiny', 'small', 'normal', 'large', 'huge'], group='Signals')

// ═════════ 高级警报配置 ════════
advancedAlertSet = input(false, '═════════ Advanced Alert Configuration ════════')
enable_advanced_alerts = input.bool(true, title='启用高级警报系统', group='AdvancedAlerts')
time_window_5min = input.int(5, title='条件2时间窗口（分钟）', minval=1, maxval=30, group='AdvancedAlerts')
time_window_15min = input.int(15, title='条件3时间窗口（分钟）', minval=5, maxval=60, group='AdvancedAlerts')
time_window_10min = input.int(10, title='条件4时间窗口（分钟）', minval=5, maxval=30, group='AdvancedAlerts')
require_all_conditions = input.bool(true, title='要求所有条件都满足', group='AdvancedAlerts')
alert_cooldown_minutes = input.int(1, title='警报冷却时间（分钟）', minval=1, maxval=120, group='AdvancedAlerts')

// ═════════ 成交量过滤配置 ════════
volumeFilterSet = input(false, '═════════ Volume Filter Configuration ════════')
enable_volume_filter = input.bool(true, title='启用成交量过滤', group='VolumeFilter')
vol_filter_length = input.int(5, title='成交量过滤长度', minval=1, maxval=20, group='VolumeFilter')
vol_multiplier = input.float(1.2, title='成交量倍数', minval=0.5, maxval=5.0, step=0.01, group='VolumeFilter')
volume_confirmation_bars = input.int(5, title='成交量确认K线数', minval=1, maxval=20, group='VolumeFilter')
volume_strength_multiplier = input.float(1.05, title='成交量强度倍数', minval=1.0, maxval=2.0, step=0.01, group='VolumeFilter')
volume_filter_level = input.int(3, title='成交量过滤等级 (1-5级)', minval=1, maxval=5, tooltip='1级最温和(过滤明显假信号), 5级最严格', group='VolumeFilter')
apply_volume_to_trigger = input.bool(false, title='启动条件应用成交量过滤', group='VolumeFilter')
min_volume_ratio = input.float(0.3, title='最小成交量比率（1级模式）', minval=0.1, maxval=1.0, step=0.01, group='VolumeFilter')
show_volume_debug = input.bool(true, title='显示成交量调试信息', group='VolumeFilter')

// ═════════ SRBR风格成交量过滤参数 ════════
srbr_vol_divisor = input.float(2.5, title='SRBR成交量除数', minval=1.0, maxval=5.0, step=0.1, tooltip='参考SRBR源代码的Vol/2.5逻辑', group='VolumeFilter')
volume_lookback_period = input.int(25, title='成交量回看周期', minval=5, maxval=50, tooltip='用于计算成交量梯度和阈值', group='VolumeFilter')

// ═════════ 价格标签配置 ════════
labelSet = input(false, '═════════ Price Labels Configuration ════════')
show_mean_label = input.bool(true, title='显示MEAN价格标签', group='Labels')
show_r1_label = input.bool(true, title='显示R1价格标签', group='Labels')
show_s1_label = input.bool(true, title='显示S1价格标签', group='Labels')
show_r2_label = input.bool(false, title='显示R2价格标签', group='Labels')
show_s2_label = input.bool(false, title='显示S2价格标签', group='Labels')
show_ma50_label = input.bool(true, title='显示MA50价格标签', group='Labels')
show_ma100_label = input.bool(false, title='显示MA100价格标签', group='Labels')
show_ma200_label = input.bool(true, title='显示MA200价格标签', group='Labels')

// ═════════ 线条粗细配置 ════════
lineSet = input(false, '═════════ Line Width Configuration ════════')
mean_width = input.int(3, title='MEAN线宽度', minval=1, maxval=5, group='Lines')
r1_s1_width = input.int(3, title='R1/S1线宽度', minval=1, maxval=5, group='Lines')
r2_s2_width = input.int(2, title='R2/S2线宽度', minval=1, maxval=5, group='Lines')

// ═════════ SRBR 参数 ════════
srbrSet = input(false, '═════════ SRBR Parameter ════════')
lookbackPeriod = input.int(18, 'SRBR Lookback Period', minval = 1, group = 'SRBR')
vol_len = input.int(2, 'Delta Volume Filter Length', tooltip = 'Higher input, will filter low volume boxes', group = 'SRBR')
box_withd = input.float(1, 'Adjust Box Width', maxval = 1000, minval = 0, step = 0.1, group = 'SRBR')

// ═════════ 表格大小配置 ════════
tableSet = input(false, '═════════ Table Size Configuration ════════')
table_text_size = input.string('tiny', title='表格文字大小', options=['auto', 'tiny', 'small', 'normal', 'large', 'huge'], group='Table')
table_rows = input.int(15, title='信息表格行数', minval=10, maxval=20, group='Table')
table_columns = input.int(4, title='信息表格列数', minval=2, maxval=6, group='Table')
mtf_table_rows = input.int(9, title='多时间框架表格行数', minval=6, maxval=12, group='Table')
mtf_table_columns = input.int(14, title='多时间框架表格列数', minval=8, maxval=16, group='Table')

// ═════════ 策略交易配置 ════════
strategySet = input(false, '═════════ Strategy Trading Configuration ════════')
enable_strategy = input.bool(true, title='启用策略交易', group='Strategy')
signal_hold_minutes = input.int(3, title='信号持有时间（分钟）', minval=1, maxval=30, group='Strategy', tooltip='如果在此时间内没有新的同方向信号，则平仓')
enable_json_alerts = input.bool(true, title='启用JSON格式交易通知', group='Strategy')
position_size_percent = input.float(10.0, title='仓位大小（%）', minval=1.0, maxval=100.0, step=1.0, group='Strategy')
enable_stop_loss = input.bool(false, title='启用止损', group='Strategy')
stop_loss_percent = input.float(2.0, title='止损百分比（%）', minval=0.1, maxval=10.0, step=0.1, group='Strategy')
enable_take_profit = input.bool(false, title='启用止盈', group='Strategy')
take_profit_percent = input.float(4.0, title='止盈百分比（%）', minval=0.1, maxval=20.0, step=0.1, group='Strategy')
max_concurrent_positions = input.int(1, title='最大并发持仓数', minval=1, maxval=5, group='Strategy')
enable_reverse_signals = input.bool(true, title='启用反向信号平仓', group='Strategy', tooltip='当出现反向信号时立即平仓')

// ═════════ 高级策略配置 ════════
advancedSet = input(false, '═════════ Advanced Strategy Configuration ════════')
require_strong_signals = input.bool(true, title='仅使用强信号', group='Advanced', tooltip='只有当信号强度为"强"时才交易')
enable_trend_filter = input.bool(true, title='启用趋势过滤', group='Advanced', tooltip='只在趋势方向交易')
min_channel_width = input.float(2.0, title='最小通道宽度', minval=0.1, step=0.1, group='Advanced', tooltip='MRC通道宽度小于此值时不交易')
enable_volume_filter_strategy = input.bool(true, title='策略启用成交量过滤', group='Advanced')
enable_rsi_filter = input.bool(true, title='启用RSI过滤', group='Advanced', tooltip='避免在RSI极值区域交易')
rsi_overbought_threshold = input.float(80.0, title='RSI超买阈值', minval=50.0, maxval=100.0, group='Advanced')
rsi_oversold_threshold = input.float(20.0, title='RSI超卖阈值', minval=0.0, maxval=50.0, group='Advanced')
enable_ma_distance_filter = input.bool(true, title='启用MA距离过滤', group='Advanced', tooltip='价格距离MA200太远时不交易')
max_ma_distance_percent = input.float(5.0, title='最大MA距离百分比', minval=1.0, maxval=20.0, step=0.5, group='Advanced')

//************************************************************************************************************
// 策略状态变量定义（需要在使用前定义）
//************************************************************************************************************

// ═════════ 策略状态管理变量 ═════════
var int last_signal_time = na
var string last_signal_direction = na
var bool position_opened = false
var float entry_price = na
var int entry_time = na
var string current_position_direction = na
var int daily_trade_count = 0
var float daily_pnl = 0.0
var int last_trade_day = na

// ═════════ 策略函数定义 ═════════
// 检测1分钟综合信号
detect_1m_signal() =>
    // 直接计算1分钟综合信号，避免依赖全局变量
    // 获取1分钟数据
    close_1m_current = request.security(syminfo.tickerid, "1", close, lookahead=barmerge.lookahead_off)

    // 简化的信号检测逻辑
    signal_long_1m = mrc_srbr_both_bullish
    signal_short_1m = mrc_srbr_both_bearish

    signal_direction = signal_long_1m ? "LONG" : signal_short_1m ? "SHORT" : na
    [signal_long_1m, signal_short_1m, signal_direction]

// 检查是否超过信号持有时间
is_signal_expired() =>
    if na(last_signal_time)
        false
    else
        time_diff = (time - last_signal_time) / 60000  // 转换为分钟
        time_diff > signal_hold_minutes

// 生成JSON格式的交易通知
generate_json_alert(action, direction, price, reason) =>
    json_msg = '{"action":"' + action + '","direction":"' + direction + '","symbol":"' + syminfo.ticker + '","timeframe":"' + timeframe.period + '","price":' + str.tostring(price, '#.####') + ',"time":"' + str.format_time(time, "yyyy-MM-dd HH:mm:ss") + '","reason":"' + reason + '","position_size":' + str.tostring(position_size_percent) + '}'
    json_msg

// 删除不再使用的斜率阈值计算

// ═════════ RSI 参数 ════════
rsiSet = input(false, '═════════ RSI Parameter ════════')
rsi_src = input.source(close, 'RSI Calculation Source', group='RSI')
rsiLength = input.int(14, 'RSI Length', minval=2, group='RSI')
smooth = input.bool(true, 'Smooth RSI?', group='RSI')
maType = input.string('Ema', 'Moving Average Type', options=['SMA', 'Hull', 'Ema', 'Wma', 'DEMA', 'RMA', 'LINREG', 'TEMA', 'ALMA', 'T3'], group='RSI')
smoothP = input.int(4, 'Smoothing Period', group='RSI')
sig = input.int(6, 'Sigma for ALMA', group='RSI')

// ═════════ 多时间框架设置 ════════
mtfSet = input(false, '═════════ Multi-Timeframe Settings ════════')
show_mtf_table = input.bool(true, title='显示多时间框架表格', group='MTF')
mtf_1m = input.bool(true, title='显示1分钟数据', group='MTF')
mtf_5m = input.bool(true, title='显示5分钟数据', group='MTF')
mtf_15m = input.bool(true, title='显示15分钟数据', group='MTF')
mtf_30m = input.bool(true, title='显示30分钟数据', group='MTF')
mtf_45m = input.bool(true, title='显示45分钟数据', group='MTF')
mtf_1h = input.bool(true, title='显示1小时数据', group='MTF')
mtf_4h = input.bool(true, title='显示4小时数据', group='MTF')

// ═════════ 显示设置 ════════
displaySet = input(false, '═════════ Display Settings ════════')
show_info_table = input.bool(false, title='显示信息表格', group='Display')
show_rsi_table = input.bool(false, title='显示RSI表格', group='Display')
table_position = input.string('top_right', title='表格位置', options=['top_left', 'top_center', 'top_right', 'middle_left', 'middle_center', 'middle_right', 'bottom_left', 'bottom_center', 'bottom_right'], group='Display')

//************************************************************************************************************
// 辅助函数定义
//************************************************************************************************************

// ═════════ 成交量分析函数（改进版本） ════════
// Delta Volume Function - 区分买卖成交量
upAndDownVolume() =>
    // 简化版本：根据K线颜色判断成交量性质
    if close > open
        volume  // 阳线，正成交量
    else if close < open
        -volume  // 阴线，负成交量
    else
        0.0  // 十字星，中性成交量

// 成交量过滤函数（1-5级细分过滤，参考SRBR源代码逻辑）
// 等级说明：
// 1级：最温和 - 只过滤极明显的假信号，适合高频交易
// 2级：温和 - 过滤明显假信号，保持较高的信号频率
// 3级：中等 - 标准过滤强度，平衡信号质量和频率
// 4级：严格 - 较强过滤，减少假信号但可能错过部分机会
// 5级：最严格 - 最强过滤，参考SRBR的高低成交量阈值逻辑，信号最少但质量最高
get_volume_filter() =>
    Vol = upAndDownVolume()
    vol_abs = math.abs(Vol)
    vol_avg = ta.sma(vol_abs, vol_filter_length)

    if not enable_volume_filter
        [true, true]  // 如果未启用成交量过滤，返回true
    else
        // SRBR风格的成交量阈值计算（参考原始SRBR代码）
        vol_adjusted = Vol / srbr_vol_divisor  // 参考SRBR的Vol/2.5逻辑
        vol_hi = ta.highest(vol_adjusted, volume_lookback_period)  // 高成交量阈值
        vol_lo = ta.lowest(vol_adjusted, volume_lookback_period)   // 低成交量阈值
        vol_range = vol_hi - vol_lo
        vol_mid = (vol_hi + vol_lo) / 2

        // 根据1-5级过滤等级调整阈值（参考SRBR的分层逻辑）
        threshold_multiplier = switch volume_filter_level
            1 => vol_multiplier * 0.6   // 1级：最温和，只过滤极明显的假信号
            2 => vol_multiplier * 0.8   // 2级：温和，过滤明显假信号
            3 => vol_multiplier * 1.0   // 3级：中等，标准过滤
            4 => vol_multiplier * 1.3   // 4级：严格，较强过滤
            5 => vol_multiplier * 1.6   // 5级：最严格，最强过滤
            => vol_multiplier

        vol_threshold = vol_avg * threshold_multiplier

        // 成交量方向性过滤（结合SRBR的正负成交量概念）
        long_volume_ok = false
        short_volume_ok = false

        // 百分位阈值（0~1），用于稳健跨品种/时段比较
        vol_pr = ta.percentrank(vol_abs, volume_lookback_period)
        pr_req = switch volume_filter_level
            1 => 0.55
            2 => 0.65
            3 => 0.75
            4 => 0.85
            5 => 0.9
            => 0.75

        if volume_filter_level == 1
            // 1级：最温和过滤，只要成交量方向正确且不是极低成交量
            min_volume_threshold = vol_avg * min_volume_ratio
            long_volume_ok := Vol > 0 and vol_abs > min_volume_threshold and vol_pr > pr_req
            short_volume_ok := Vol < 0 and vol_abs > min_volume_threshold and vol_pr > pr_req
        else if volume_filter_level == 2
            // 2级：温和过滤，需要成交量超过平均值的80%
            moderate_threshold = vol_avg * 0.8
            long_volume_ok := Vol > 0 and vol_abs > moderate_threshold and vol_pr > pr_req
            short_volume_ok := Vol < 0 and vol_abs > moderate_threshold and vol_pr > pr_req
        else if volume_filter_level == 3
            // 3级：中等过滤，标准阈值 + 百分位
            long_volume_ok := Vol > 0 and vol_abs > vol_threshold and vol_pr > pr_req
            short_volume_ok := Vol < 0 and vol_abs > vol_threshold and vol_pr > pr_req
        else if volume_filter_level == 4
            // 4级：严格过滤，需要成交量超过阈值且接近高成交量区间，同时通过百分位
            strict_threshold = vol_threshold * 1.2
            volume_strength_check = vol_abs > vol_mid  // 需要超过中位数
            long_volume_ok := Vol > 0 and vol_abs > strict_threshold and volume_strength_check and vol_pr > pr_req
            short_volume_ok := Vol < 0 and vol_abs > strict_threshold and volume_strength_check and vol_pr > pr_req
        else  // volume_filter_level == 5
            // 5级：最严格过滤，参考SRBR的vol_hi/vol_lo逻辑 + 高百分位
            very_strict_threshold = vol_threshold * 1.5
            // 做多需要正成交量且接近高成交量阈值（类似SRBR的Vol > vol_hi）
            long_volume_ok := Vol > 0 and vol_abs > very_strict_threshold and vol_adjusted > vol_hi * 0.8 and vol_pr > pr_req
            // 做空需要负成交量且接近低成交量阈值（类似SRBR的Vol < vol_lo）
            short_volume_ok := Vol < 0 and vol_abs > very_strict_threshold and vol_adjusted < vol_lo * 1.2 and vol_pr > pr_req

        [long_volume_ok, short_volume_ok]

// 成交量趋势确认函数（1-5级细分版本）
volume_trend_confirmation(signal_type) =>
    if not enable_volume_filter
        true
    else
        Vol = upAndDownVolume()
        volume_avg = ta.sma(math.abs(Vol), volume_confirmation_bars)
        current_volume = math.abs(Vol)

        // SRBR风格的成交量分析
        vol_adjusted = Vol / srbr_vol_divisor
        vol_hi = ta.highest(vol_adjusted, volume_lookback_period)
        vol_lo = ta.lowest(vol_adjusted, volume_lookback_period)

        // 根据1-5级过滤等级调整成交量强度要求
        strength_multiplier = switch volume_filter_level
            1 => volume_strength_multiplier * 0.8   // 1级：最宽松的强度要求
            2 => volume_strength_multiplier * 0.9   // 2级：较宽松的强度要求
            3 => volume_strength_multiplier * 1.0   // 3级：标准强度要求
            4 => volume_strength_multiplier * 1.2   // 4级：较严格的强度要求
            5 => volume_strength_multiplier * 1.5   // 5级：最严格的强度要求
            => volume_strength_multiplier

        volume_strength = current_volume > volume_avg * strength_multiplier

        // 根据过滤等级应用不同的确认逻辑
        if volume_filter_level <= 2
            // 1-2级：基础成交量方向确认
            if signal_type == "long"
                Vol > 0 and volume_strength
            else if signal_type == "short"
                Vol < 0 and volume_strength
            else
                volume_strength
        else if volume_filter_level == 3
            // 3级：标准确认逻辑
            if signal_type == "long"
                Vol > 0 and volume_strength
            else if signal_type == "short"
                Vol < 0 and volume_strength
            else
                volume_strength
        else if volume_filter_level == 4
            // 4级：严格确认，需要额外的成交量条件
            volume_momentum = current_volume > volume_avg * 1.1  // 额外的动量要求
            if signal_type == "long"
                Vol > 0 and volume_strength and volume_momentum
            else if signal_type == "short"
                Vol < 0 and volume_strength and volume_momentum
            else
                volume_strength and volume_momentum
        else  // volume_filter_level == 5
            // 5级：最严格确认，参考SRBR的高低成交量阈值
            if signal_type == "long"
                // 做多需要正成交量、强度确认、且接近高成交量阈值
                Vol > 0 and volume_strength and vol_adjusted > vol_hi * 0.7
            else if signal_type == "short"
                // 做空需要负成交量、强度确认、且接近低成交量阈值
                Vol < 0 and volume_strength and vol_adjusted < vol_lo * 1.3
            else
                volume_strength and (vol_adjusted > vol_hi * 0.7 or vol_adjusted < vol_lo * 1.3)

// ═════════ SRBR 支撑阻力计算函数 ════════
// SRBR Delta Volume Function (与2.pine保持一致)
upAndDownVolumeSRBR() =>
    posVol = 0.0
    negVol = 0.0
    var isBuyVolume = true

    switch
        close > open =>
    	    isBuyVolume := true
    	    isBuyVolume
        close < open =>
    	    isBuyVolume := false
    	    isBuyVolume

    if isBuyVolume
        posVol := posVol + volume
        posVol
    else
        negVol := negVol - volume
        negVol

    posVol + negVol

// SRBR 支撑阻力计算函数（与2.pine保持一致，但无绘图操作）
calcSupportResistance(src, lookback) =>
    // Volume
    Vol = upAndDownVolumeSRBR()
    vol_hi = ta.highest(Vol / 2.5, vol_len)
    vol_lo = ta.lowest(Vol / 2.5, vol_len)

    var float supportLevel = na
    var float supportLevel_1 = na
    var float resistanceLevel = na
    var float resistanceLevel_1 = na
    var bool brekout_res = false
    var bool brekout_sup = false
    var bool res_holds = false
    var bool sup_holds = false

    // Find pivot points
    pivotHigh = ta.pivothigh(src, lookback, lookback)
    pivotLow = ta.pivotlow(src, lookback, lookback)
    // Box width
    atr = ta.atr(200)
    withd = atr * box_withd

    // Find support levels with Positive Volume
    if not na(pivotLow) and Vol > vol_hi
        supportLevel := pivotLow
        supportLevel_1 := supportLevel - withd

    // Find resistance levels with Negative Volume
    if not na(pivotHigh) and Vol < vol_lo
        resistanceLevel := pivotHigh
        resistanceLevel_1 := resistanceLevel + withd

    // Break of support or resistance conditions
    brekout_res := ta.crossover(low, resistanceLevel_1)
    res_holds := ta.crossunder(high, resistanceLevel)
    sup_holds := ta.crossover(low, supportLevel)
    brekout_sup := ta.crossunder(high, supportLevel_1)

    [supportLevel, resistanceLevel, brekout_res, res_holds, sup_holds, brekout_sup]

// SRBR 多时间周期专用计算函数（与2.pine保持一致）
calcSRForMTF(src, lookback) =>
    // Delta Volume Function (与主函数相同)
    posVol = 0.0
    negVol = 0.0
    var isBuyVolume = true

    switch
        close > open =>
    	    isBuyVolume := true
    	    isBuyVolume
        close < open =>
    	    isBuyVolume := false
    	    isBuyVolume

    if isBuyVolume
        posVol := posVol + volume
        posVol
    else
        negVol := negVol - volume
        negVol

    Vol = posVol + negVol

    // 成交量过滤阈值
    vol_hi = ta.highest(Vol / 2.5, vol_len)
    vol_lo = ta.lowest(Vol / 2.5, vol_len)

    // 支撑阻力位识别（与主函数相同的逻辑）
    var float mtf_support = na
    var float mtf_resistance = na

    pivotHigh = ta.pivothigh(src, lookback, lookback)
    pivotLow = ta.pivotlow(src, lookback, lookback)

    // 支撑位：需要正成交量且大于高阈值
    if not na(pivotLow) and Vol > vol_hi
        mtf_support := pivotLow

    // 阻力位：需要负成交量且小于低阈值
    if not na(pivotHigh) and Vol < vol_lo
        mtf_resistance := pivotHigh

    // 突破判断逻辑
    atr = ta.atr(200)
    withd = atr * box_withd

    mtf_support_1 = mtf_support - withd
    mtf_resistance_1 = mtf_resistance + withd

    mtf_brekout_res = ta.crossover(low, mtf_resistance_1)
    mtf_res_holds = ta.crossunder(high, mtf_resistance)
    mtf_sup_holds = ta.crossover(low, mtf_support)
    mtf_brekout_sup = ta.crossunder(high, mtf_support_1)

    [mtf_support, mtf_resistance, mtf_brekout_res, mtf_res_holds, mtf_sup_holds, mtf_brekout_sup]

// SRBR 单独获取支撑位函数
getSupportLevel(src, lookback) =>
    [support, resistance, brekout_res, res_holds, sup_holds, brekout_sup] = calcSRForMTF(src, lookback)
    support

// SRBR 单独获取阻力位函数
getResistanceLevel(src, lookback) =>
    [support, resistance, brekout_res, res_holds, sup_holds, brekout_sup] = calcSRForMTF(src, lookback)
    resistance

// SRBR 单独获取阻力突破函数
getBrekoutRes(src, lookback) =>
    [support, resistance, brekout_res, res_holds, sup_holds, brekout_sup] = calcSRForMTF(src, lookback)
    brekout_res

// SRBR 单独获取阻力保持函数
getResHolds(src, lookback) =>
    [support, resistance, brekout_res, res_holds, sup_holds, brekout_sup] = calcSRForMTF(src, lookback)
    res_holds

// SRBR 单独获取支撑保持函数
getSupHolds(src, lookback) =>
    [support, resistance, brekout_res, res_holds, sup_holds, brekout_sup] = calcSRForMTF(src, lookback)
    sup_holds

// SRBR 单独获取支撑突破函数
getBrekoutSup(src, lookback) =>
    [support, resistance, brekout_res, res_holds, sup_holds, brekout_sup] = calcSRForMTF(src, lookback)
    brekout_sup

// SRBR 价格状态判断函数
get_price_status_srbr(price_val, support_val, resistance_val) =>
    if na(price_val) or na(support_val) or na(resistance_val)
        "N/A"
    else if price_val > resistance_val
        "看多"
    else if price_val < support_val
        "看空"
    else
        "震荡"

// SRBR 价格状态颜色函数
get_price_status_color_srbr(price_val, support_val, resistance_val) =>
    if na(price_val) or na(support_val) or na(resistance_val)
        color.new(color.gray, 60)
    else if price_val > resistance_val
        color.new(color.green, 40)  // 看多 - 绿色
    else if price_val < support_val
        color.new(color.red, 40)    // 看空 - 红色
    else
        color.new(color.orange, 40) // 震荡 - 橙色

// SRBR 距离计算函数
get_distance_to_levels_srbr(price_val, support_val, resistance_val) =>
    if na(price_val) or na(support_val) or na(resistance_val)
        [na, na]
    else
        distance_to_support = math.abs(price_val - support_val)
        distance_to_resistance = math.abs(price_val - resistance_val)
        [distance_to_support, distance_to_resistance]

// ═════════ 距离阈值切换（参考原MA200代码） ════════
get_distance_threshold() =>
    sym = ticker.standard(syminfo.tickerid)
    switch sym
        'TICKMILL:BTCUSD' => btc_dist
        'TICKMILL:XAUUSD' => xau_dist
        'TICKMILL:ETHUSD' => eth_dist
        'TICKMILL:GBPJPY' => gbp_dist
        => default_dist

// ═════════ R1/S1距离阈值切换 ════════
get_r1s1_distance_threshold() =>
    sym = ticker.standard(syminfo.tickerid)
    switch sym
        'TICKMILL:BTCUSD' => btc_r1s1_dist
        'TICKMILL:XAUUSD' => xau_r1s1_dist
        'TICKMILL:ETHUSD' => eth_r1s1_dist
        'TICKMILL:GBPJPY' => gbp_r1s1_dist
        => default_r1s1_dist

// ═════════ 表格文字大小转换 ════════
get_text_size() =>
    switch table_text_size
        'auto' => size.auto
        'tiny' => size.tiny
        'small' => size.small
        'normal' => size.normal
        'large' => size.large
        'huge' => size.huge
        => size.tiny

// ═════════ 距离背景颜色函数 ════════
get_distance_bg_color(distance_value, threshold) =>
    if na(distance_value)
        color.new(color.gray, 80)  // 无数据时为灰色
    else if distance_value > threshold
        color.new(color.green, 80)  // 距离大于阈值时为浅绿色
    else
        color.new(color.red, 80)    // 距离小于阈值时为浅红色

// ═════════ 箭头大小转换 ════════
get_arrow_size() =>
    switch arrow_size
        'tiny' => size.tiny
        'small' => size.small
        'normal' => size.normal
        'large' => size.large
        'huge' => size.huge
        => size.large

// ═════════ 多时间框架辅助函数 ════════
// 获取信号颜色
get_signal_color(osc_sig, long_sig, short_sig) =>
    osc_sig ? color.new(color.yellow, 60) : long_sig ? color.new(color.green, 40) : short_sig ? color.new(color.red, 40) : color.new(color.gray, 80)

// 获取信号文本
get_signal_text(osc_sig, long_sig, short_sig) =>
    osc_sig ? "震荡⚡" : long_sig ? "做多🚀" : short_sig ? "做空🔻" : "观望"

// 获取最终判断
get_final_judgment(osc_sig, long_sig, short_sig) =>
    osc_sig ? "✓震荡" : long_sig ? "✓做多" : short_sig ? "✓做空" : "观望"

// ═════════ 综合信号判断函数 ════════
// 根据MRC信号和SRBR信号的组合来确定最终信号
get_combined_signal(mrc_signal, srbr_signal) =>
    // mrc_signal: "做多", "做空", "震荡"
    // srbr_signal: "看多", "看空", "震荡"

    if (mrc_signal == "做多" and srbr_signal == "看多")
        "做多"
    else if (mrc_signal == "做空" and srbr_signal == "看空")
        "做空"
    else
        "震荡"  // 所有其他情况都判断为震荡

// 获取综合信号颜色
get_combined_signal_color(combined_signal) =>
    switch combined_signal
        "做多" => color.new(color.green, 20)   // 深绿色
        "做空" => color.new(color.red, 20)     // 深红色
        "震荡" => color.new(color.orange, 20)  // 深橙色
        => color.new(color.gray, 60)

// 获取信号强度（根据两个指标的一致性）
get_signal_strength(mrc_signal, srbr_signal) =>
    if (mrc_signal == "做多" and srbr_signal == "看多") or (mrc_signal == "做空" and srbr_signal == "看空")
        "强"  // 两个指标方向一致
    else if (mrc_signal == "震荡" or srbr_signal == "震荡")
        "弱"  // 其中一个指标是震荡
    else
        "冲突"  // 两个指标方向相反

// 获取RSI状态颜色（根据条件标记）
get_rsi_color(rsi_val, long_threshold, short_threshold, is_neutral, is_overbought, is_oversold) =>
    if is_neutral
        color.new(color.orange, 40)  // 震荡条件
    else if is_overbought
        color.new(color.green, 40)   // 做多条件
    else if is_oversold
        color.new(color.red, 40)     // 做空条件
    else
        color.white

// 获取价格位置颜色（根据条件标记）
get_price_color(close_val, upband1_val, loband1_val, in_channel, above_r1, below_s1) =>
    if in_channel
        color.new(color.orange, 40)  // 震荡条件
    else if above_r1
        color.new(color.green, 40)   // 做多条件
    else if below_s1
        color.new(color.red, 40)     // 做空条件
    else
        color.white

// 获取MA50位置颜色（根据条件标记）
get_ma50_color(ma50_val, meanline_val, above_mean, below_mean) =>
    if above_mean
        color.new(color.green, 40)   // 做多条件
    else if below_mean
        color.new(color.red, 40)     // 做空条件
    else
        color.white

// 获取MA200距离颜色（根据条件标记）
get_ma200_distance_color(distance_val, threshold_val, is_near, is_far) =>
    if is_near
        color.new(color.orange, 40)  // 震荡条件
    else if is_far
        color.new(color.gray, 60)    // 趋势条件（做多/做空都需要远离MA200）
    else
        color.white

// 获取价格位置文本
get_price_position(close_val, upband1_val, loband1_val) =>
    if na(close_val) or na(upband1_val) or na(loband1_val)
        "N/A"
    else if close_val > upband1_val
        "R1上"
    else if close_val < loband1_val
        "S1下"
    else
        "通道内"

// 获取价格与MEAN位置关系文本
get_price_mean_position(close_val, meanline_val) =>
    if na(close_val) or na(meanline_val)
        "N/A"
    else if close_val > meanline_val
        "偏多"
    else if close_val < meanline_val
        "偏空"
    else
        "平衡"

// 获取价格与MEAN位置关系颜色
get_price_mean_color(close_val, meanline_val) =>
    if na(close_val) or na(meanline_val)
        color.white
    else if close_val > meanline_val
        color.new(color.green, 40)   // 偏多 - 绿色
    else if close_val < meanline_val
        color.new(color.red, 40)     // 偏空 - 红色
    else
        color.new(color.gray, 40)    // 平衡 - 灰色

// 获取MA50位置文本
get_ma50_position(ma50_val, meanline_val) =>
    if na(ma50_val) or na(meanline_val)
        "N/A"
    else if ma50_val > meanline_val
        "MEAN上"
    else if ma50_val < meanline_val
        "MEAN下"
    else
        "MEAN附近"

// 获取RSI状态文本
get_rsi_status(rsi_val, long_threshold, short_threshold) =>
    if na(rsi_val)
        "N/A"
    else if rsi_val > long_threshold
        "超买"
    else if rsi_val < short_threshold
        "超卖"
    else
        "震荡"



// SuperSmoother 函数（与原始MRC保持一致）
supersmoother(src, len) =>
    pi = 2 * math.asin(1)
    s_a1 = math.exp(-math.sqrt(2) * pi / len)
    s_b1 = 2 * s_a1 * math.cos(math.sqrt(2) * pi / len)
    s_c3 = -math.pow(s_a1, 2)
    s_c2 = s_b1
    s_c1 = 1 - s_c2 - s_c3
    ss = 0.0
    ss := s_c1 * src + s_c2 * nz(ss[1], src[1]) + s_c3 * nz(ss[2], src[2])
    ss

// SAK 平滑函数
SAK_smoothing(type, src, len) =>
    switch type
        'SMA' => ta.sma(src, len)
        'EMA' => ta.ema(src, len)
        'RMA' => ta.rma(src, len)
        'Gaussian' => ta.sma(src, len)  // 简化版本
        'Butterworth' => ta.sma(src, len)  // 简化版本
        'BandStop' => ta.sma(src, len)  // 简化版本
        'Ehlers EMA' => ta.ema(src, len)  // 简化版本
        => ta.sma(src, len)

// MRC 计算函数
get_mrc() =>
    v_condition = 0
    v_meanline = source
    v_meanrange = supersmoother(ta.tr, length)

    // 计算π乘数（与原始MRC保持一致）
    pi = 2 * math.asin(1)
    mult = pi * innermult
    mult2 = pi * outermult

    // 获取均线值
    if type == 'SuperSmoother'
        v_meanline := supersmoother(source, length)
    else
        v_meanline := SAK_smoothing(type, source, length)

    // 计算通道（使用π乘数）
    upband1 = v_meanline + v_meanrange * mult
    loband1 = v_meanline - v_meanrange * mult
    upband2 = v_meanline + v_meanrange * mult2
    loband2 = v_meanline - v_meanrange * mult2

    [v_meanline, v_meanrange, upband1, loband1, upband2, loband2, v_condition]

// RSI 移动平均函数
dema(src, length) =>
    ema1 = ta.ema(src, length)
    ema2 = ta.ema(ema1, length)
    2 * ema1 - ema2

tema(src, length) =>
    ema1 = ta.ema(src, length)
    ema2 = ta.ema(ema1, length)
    ema3 = ta.ema(ema2, length)
    3 * ema1 - 3 * ema2 + ema3

t3(src, length, vfactor) =>
    ema1 = ta.ema(src, length)
    ema2 = ta.ema(ema1, length)
    ema3 = ta.ema(ema2, length)
    ema4 = ta.ema(ema3, length)
    ema5 = ta.ema(ema4, length)
    ema6 = ta.ema(ema5, length)

    c1 = -vfactor * vfactor * vfactor
    c2 = 3 * vfactor * vfactor + 3 * vfactor * vfactor * vfactor
    c3 = -6 * vfactor * vfactor - 3 * vfactor - 3 * vfactor * vfactor * vfactor
    c4 = 1 + 3 * vfactor + vfactor * vfactor * vfactor + 3 * vfactor * vfactor

    c1 * ema6 + c2 * ema5 + c3 * ema4 + c4 * ema3

ma(src, len, type, almaSig) =>
    switch type
        'SMA' => ta.sma(src, len)
        'Hull' => ta.hma(src, len)
        'Ema' => ta.ema(src, len)
        'Wma' => ta.wma(src, len)
        'DEMA' => dema(src, len)
        'RMA' => ta.rma(src, len)
        'LINREG' => ta.linreg(src, len, 0)
        'TEMA' => tema(src, len)
        'ALMA' => ta.alma(src, len, 0, almaSig)
        'T3' => t3(src, len, 0.7)

// 多时间框架RSI穿越次数计算函数
mtf_rsi_segment_count(long_threshold, short_threshold) =>
    var int rsi_state = 0
    var int extreme_type = 0
    var int segment_count = 0

    rsi_val = smooth ? ma(ta.rsi(rsi_src, rsiLength), smoothP, maType, sig) : ta.rsi(rsi_src, rsiLength)
    current_state = rsi_val > long_threshold ? 1 : rsi_val < short_threshold ? -1 : 0

    if current_state != rsi_state and not na(rsi_val)
        if current_state == 1
            if extreme_type != 1
                extreme_type := 1
                segment_count := 1
            else
                segment_count := segment_count + 1
        else if current_state == -1
            if extreme_type != -1
                extreme_type := -1
                segment_count := -1
            else
                segment_count := segment_count - 1

        rsi_state := current_state

    math.abs(segment_count)

// 计算多时间框架RSI穿越次数
get_mtf_rsi_crossover_count(tf, long_threshold, short_threshold) =>
    request.security(syminfo.tickerid, tf, mtf_rsi_segment_count(long_threshold, short_threshold), lookahead=barmerge.lookahead_off)

//************************************************************************************************************
// 多时间框架计算函数
//************************************************************************************************************

// 多时间框架MRC计算函数
get_mtf_mrc(tf) =>
    request.security(syminfo.tickerid, tf, get_mrc(), lookahead=barmerge.lookahead_off)

// 多时间框架MA计算函数
get_mtf_ma(tf) =>
    ma50_mtf = request.security(syminfo.tickerid, tf, exponential ? ta.ema(close, 50) : ta.sma(close, 50), lookahead=barmerge.lookahead_off)
    ma100_mtf = request.security(syminfo.tickerid, tf, exponential ? ta.ema(close, 100) : ta.sma(close, 100), lookahead=barmerge.lookahead_off)
    ma200_mtf = request.security(syminfo.tickerid, tf, exponential ? ta.ema(close, 200) : ta.sma(close, 200), lookahead=barmerge.lookahead_off)
    close_mtf = request.security(syminfo.tickerid, tf, close, lookahead=barmerge.lookahead_off)
    [ma50_mtf, ma100_mtf, ma200_mtf, close_mtf]

// 多时间框架RSI计算函数
get_mtf_rsi(tf) =>
    rsi_mtf = request.security(syminfo.tickerid, tf, ta.rsi(rsi_src, rsiLength), lookahead=barmerge.lookahead_off)
    if smooth
        rsi_mtf := request.security(syminfo.tickerid, tf, ma(ta.rsi(rsi_src, rsiLength), smoothP, maType, sig), lookahead=barmerge.lookahead_off)
    rsi_mtf

//************************************************************************************************************
// 指标计算
//************************************************************************************************************

// ═════════ MA 计算 ═════════
src = close
ma50 = exponential ? ta.ema(src, 50) : ta.sma(src, 50)
ma100 = exponential ? ta.ema(src, 100) : ta.sma(src, 100)
ma200 = exponential ? ta.ema(src, 200) : ta.sma(src, 200)

// ═════════ MRC 计算 ═════════
[meanline, meanrange, upband1, loband1, upband2, loband2, condition] = get_mrc()

// ═════════ RSI 计算 ═════════
rsi = ta.rsi(rsi_src, rsiLength)
if smooth
    rsi := ma(rsi, smoothP, maType, sig)

// RSI 机器学习阈值计算（简化版）
var rsi_values = array.new_float(0)
if last_bar_index - bar_index <= 1000
    array.push(rsi_values, rsi)

var centroids = array.new_float(3)
if array.size(rsi_values) > 3
    array.set(centroids, 0, array.percentile_linear_interpolation(rsi_values, 25))
    array.set(centroids, 1, array.percentile_linear_interpolation(rsi_values, 50))
    array.set(centroids, 2, array.percentile_linear_interpolation(rsi_values, 75))

long_S = array.get(centroids, 2)
short_S = array.get(centroids, 0)

// ═════════ 预先计算所有多时间框架RSI穿越次数（避免在条件块内调用） ═════════
cross_count_1m = mtf_1m ? get_mtf_rsi_crossover_count("1", long_S, short_S) : na
cross_count_5m = mtf_5m ? get_mtf_rsi_crossover_count("5", long_S, short_S) : na
cross_count_15m = mtf_15m ? get_mtf_rsi_crossover_count("15", long_S, short_S) : na
cross_count_30m = mtf_30m ? get_mtf_rsi_crossover_count("30", long_S, short_S) : na
cross_count_45m = mtf_45m ? get_mtf_rsi_crossover_count("45", long_S, short_S) : na
cross_count_1h = mtf_1h ? get_mtf_rsi_crossover_count("60", long_S, short_S) : na
cross_count_4h = mtf_4h ? get_mtf_rsi_crossover_count("240", long_S, short_S) : na

// RSI 状态和计数逻辑
var int rsi_state = 0
var int extreme_type = 0
var int segment_count = 0

current_state = rsi > long_S ? 1 : rsi < short_S ? -1 : 0

if current_state != rsi_state and not na(rsi)
    if current_state == 1
        if extreme_type != 1
            extreme_type := 1
            segment_count := 1
        else
            segment_count := segment_count + 1
    else if current_state == -1
        if extreme_type != -1
            extreme_type := -1
            segment_count := -1
        else
            segment_count := segment_count - 1

    rsi_state := current_state

// ═════════ SRBR 计算 ═════════
// 计算当前时间周期的支撑阻力位
[supportLevel, resistanceLevel, brekout_res, res_holds, sup_holds, brekout_sup] = calcSupportResistance(close, lookbackPeriod)

// 检查阻力转支撑或支撑转阻力
var bool res_is_sup = false
var bool sup_is_res = false

switch
    brekout_res =>
	    res_is_sup := true
	    res_is_sup
    res_holds =>
	    res_is_sup := false
	    res_is_sup

switch
    brekout_sup =>
	    sup_is_res := true
	    sup_is_res
    sup_holds =>
	    sup_is_res := false
	    sup_is_res

// ═════════ 多时间框架数据获取 ═════════
// 直接获取多时间框架数据（简化方法）

// 1分钟数据
rsi_1m = mtf_1m ? get_mtf_rsi("1") : na
close_1m = mtf_1m ? request.security(syminfo.tickerid, "1", close, lookahead=barmerge.lookahead_off) : na
ma50_1m = mtf_1m ? request.security(syminfo.tickerid, "1", exponential ? ta.ema(close, 50) : ta.sma(close, 50), lookahead=barmerge.lookahead_off) : na
ma200_1m = mtf_1m ? request.security(syminfo.tickerid, "1", exponential ? ta.ema(close, 200) : ta.sma(close, 200), lookahead=barmerge.lookahead_off) : na
meanline_1m = mtf_1m ? request.security(syminfo.tickerid, "1", supersmoother(source, length), lookahead=barmerge.lookahead_off) : na
meanrange_1m = mtf_1m ? request.security(syminfo.tickerid, "1", supersmoother(ta.tr, length), lookahead=barmerge.lookahead_off) : na
upband1_1m = mtf_1m and not na(meanline_1m) and not na(meanrange_1m) ? meanline_1m + meanrange_1m * (2 * math.asin(1) * innermult) : na
loband1_1m = mtf_1m and not na(meanline_1m) and not na(meanrange_1m) ? meanline_1m - meanrange_1m * (2 * math.asin(1) * innermult) : na
// SRBR 1分钟数据 - 使用单独函数获取每个值
supportLevel_1m = mtf_1m ? request.security(syminfo.tickerid, "1", getSupportLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
resistanceLevel_1m = mtf_1m ? request.security(syminfo.tickerid, "1", getResistanceLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
brekout_res_1m = mtf_1m ? request.security(syminfo.tickerid, "1", getBrekoutRes(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
res_holds_1m = mtf_1m ? request.security(syminfo.tickerid, "1", getResHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
sup_holds_1m = mtf_1m ? request.security(syminfo.tickerid, "1", getSupHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
brekout_sup_1m = mtf_1m ? request.security(syminfo.tickerid, "1", getBrekoutSup(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false

// 5分钟数据
rsi_5m = mtf_5m ? get_mtf_rsi("5") : na
close_5m = mtf_5m ? request.security(syminfo.tickerid, "5", close, lookahead=barmerge.lookahead_off) : na
ma50_5m = mtf_5m ? request.security(syminfo.tickerid, "5", exponential ? ta.ema(close, 50) : ta.sma(close, 50), lookahead=barmerge.lookahead_off) : na
ma200_5m = mtf_5m ? request.security(syminfo.tickerid, "5", exponential ? ta.ema(close, 200) : ta.sma(close, 200), lookahead=barmerge.lookahead_off) : na
meanline_5m = mtf_5m ? request.security(syminfo.tickerid, "5", supersmoother(source, length), lookahead=barmerge.lookahead_off) : na
meanrange_5m = mtf_5m ? request.security(syminfo.tickerid, "5", supersmoother(ta.tr, length), lookahead=barmerge.lookahead_off) : na
upband1_5m = mtf_5m and not na(meanline_5m) and not na(meanrange_5m) ? meanline_5m + meanrange_5m * (2 * math.asin(1) * innermult) : na
loband1_5m = mtf_5m and not na(meanline_5m) and not na(meanrange_5m) ? meanline_5m - meanrange_5m * (2 * math.asin(1) * innermult) : na
// SRBR 5分钟数据 - 使用单独函数获取每个值
supportLevel_5m = mtf_5m ? request.security(syminfo.tickerid, "5", getSupportLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
resistanceLevel_5m = mtf_5m ? request.security(syminfo.tickerid, "5", getResistanceLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
brekout_res_5m = mtf_5m ? request.security(syminfo.tickerid, "5", getBrekoutRes(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
res_holds_5m = mtf_5m ? request.security(syminfo.tickerid, "5", getResHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
sup_holds_5m = mtf_5m ? request.security(syminfo.tickerid, "5", getSupHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
brekout_sup_5m = mtf_5m ? request.security(syminfo.tickerid, "5", getBrekoutSup(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false

// 15分钟数据
rsi_15m = mtf_15m ? get_mtf_rsi("15") : na
close_15m = mtf_15m ? request.security(syminfo.tickerid, "15", close, lookahead=barmerge.lookahead_off) : na
ma50_15m = mtf_15m ? request.security(syminfo.tickerid, "15", exponential ? ta.ema(close, 50) : ta.sma(close, 50), lookahead=barmerge.lookahead_off) : na
ma200_15m = mtf_15m ? request.security(syminfo.tickerid, "15", exponential ? ta.ema(close, 200) : ta.sma(close, 200), lookahead=barmerge.lookahead_off) : na
meanline_15m = mtf_15m ? request.security(syminfo.tickerid, "15", supersmoother(source, length), lookahead=barmerge.lookahead_off) : na
meanrange_15m = mtf_15m ? request.security(syminfo.tickerid, "15", supersmoother(ta.tr, length), lookahead=barmerge.lookahead_off) : na
upband1_15m = mtf_15m and not na(meanline_15m) and not na(meanrange_15m) ? meanline_15m + meanrange_15m * (2 * math.asin(1) * innermult) : na
loband1_15m = mtf_15m and not na(meanline_15m) and not na(meanrange_15m) ? meanline_15m - meanrange_15m * (2 * math.asin(1) * innermult) : na
// SRBR 15分钟数据 - 使用单独函数获取每个值
supportLevel_15m = mtf_15m ? request.security(syminfo.tickerid, "15", getSupportLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
resistanceLevel_15m = mtf_15m ? request.security(syminfo.tickerid, "15", getResistanceLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
brekout_res_15m = mtf_15m ? request.security(syminfo.tickerid, "15", getBrekoutRes(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
res_holds_15m = mtf_15m ? request.security(syminfo.tickerid, "15", getResHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
sup_holds_15m = mtf_15m ? request.security(syminfo.tickerid, "15", getSupHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
brekout_sup_15m = mtf_15m ? request.security(syminfo.tickerid, "15", getBrekoutSup(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false

// 30分钟数据
rsi_30m = mtf_30m ? get_mtf_rsi("30") : na
close_30m = mtf_30m ? request.security(syminfo.tickerid, "30", close, lookahead=barmerge.lookahead_off) : na
ma50_30m = mtf_30m ? request.security(syminfo.tickerid, "30", exponential ? ta.ema(close, 50) : ta.sma(close, 50), lookahead=barmerge.lookahead_off) : na
ma200_30m = mtf_30m ? request.security(syminfo.tickerid, "30", exponential ? ta.ema(close, 200) : ta.sma(close, 200), lookahead=barmerge.lookahead_off) : na
meanline_30m = mtf_30m ? request.security(syminfo.tickerid, "30", supersmoother(source, length), lookahead=barmerge.lookahead_off) : na
meanrange_30m = mtf_30m ? request.security(syminfo.tickerid, "30", supersmoother(ta.tr, length), lookahead=barmerge.lookahead_off) : na
upband1_30m = mtf_30m and not na(meanline_30m) and not na(meanrange_30m) ? meanline_30m + meanrange_30m * (2 * math.asin(1) * innermult) : na
loband1_30m = mtf_30m and not na(meanline_30m) and not na(meanrange_30m) ? meanline_30m - meanrange_30m * (2 * math.asin(1) * innermult) : na
// SRBR 30分钟数据 - 使用单独函数获取每个值
supportLevel_30m = mtf_30m ? request.security(syminfo.tickerid, "30", getSupportLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
resistanceLevel_30m = mtf_30m ? request.security(syminfo.tickerid, "30", getResistanceLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
brekout_res_30m = mtf_30m ? request.security(syminfo.tickerid, "30", getBrekoutRes(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
res_holds_30m = mtf_30m ? request.security(syminfo.tickerid, "30", getResHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
sup_holds_30m = mtf_30m ? request.security(syminfo.tickerid, "30", getSupHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
brekout_sup_30m = mtf_30m ? request.security(syminfo.tickerid, "30", getBrekoutSup(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false

// 45分钟数据
rsi_45m = mtf_45m ? get_mtf_rsi("45") : na
close_45m = mtf_45m ? request.security(syminfo.tickerid, "45", close, lookahead=barmerge.lookahead_off) : na
ma50_45m = mtf_45m ? request.security(syminfo.tickerid, "45", exponential ? ta.ema(close, 50) : ta.sma(close, 50), lookahead=barmerge.lookahead_off) : na
ma200_45m = mtf_45m ? request.security(syminfo.tickerid, "45", exponential ? ta.ema(close, 200) : ta.sma(close, 200), lookahead=barmerge.lookahead_off) : na
meanline_45m = mtf_45m ? request.security(syminfo.tickerid, "45", supersmoother(source, length), lookahead=barmerge.lookahead_off) : na
meanrange_45m = mtf_45m ? request.security(syminfo.tickerid, "45", supersmoother(ta.tr, length), lookahead=barmerge.lookahead_off) : na
upband1_45m = mtf_45m and not na(meanline_45m) and not na(meanrange_45m) ? meanline_45m + meanrange_45m * (2 * math.asin(1) * innermult) : na
loband1_45m = mtf_45m and not na(meanline_45m) and not na(meanrange_45m) ? meanline_45m - meanrange_45m * (2 * math.asin(1) * innermult) : na
// SRBR 45分钟数据 - 使用单独函数获取每个值
supportLevel_45m = mtf_45m ? request.security(syminfo.tickerid, "45", getSupportLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
resistanceLevel_45m = mtf_45m ? request.security(syminfo.tickerid, "45", getResistanceLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
brekout_res_45m = mtf_45m ? request.security(syminfo.tickerid, "45", getBrekoutRes(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
res_holds_45m = mtf_45m ? request.security(syminfo.tickerid, "45", getResHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
sup_holds_45m = mtf_45m ? request.security(syminfo.tickerid, "45", getSupHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
brekout_sup_45m = mtf_45m ? request.security(syminfo.tickerid, "45", getBrekoutSup(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false

// 1小时数据
rsi_1h = mtf_1h ? get_mtf_rsi("60") : na
close_1h = mtf_1h ? request.security(syminfo.tickerid, "60", close, lookahead=barmerge.lookahead_off) : na
ma50_1h = mtf_1h ? request.security(syminfo.tickerid, "60", exponential ? ta.ema(close, 50) : ta.sma(close, 50), lookahead=barmerge.lookahead_off) : na
ma200_1h = mtf_1h ? request.security(syminfo.tickerid, "60", exponential ? ta.ema(close, 200) : ta.sma(close, 200), lookahead=barmerge.lookahead_off) : na
meanline_1h = mtf_1h ? request.security(syminfo.tickerid, "60", supersmoother(source, length), lookahead=barmerge.lookahead_off) : na
meanrange_1h = mtf_1h ? request.security(syminfo.tickerid, "60", supersmoother(ta.tr, length), lookahead=barmerge.lookahead_off) : na
upband1_1h = mtf_1h and not na(meanline_1h) and not na(meanrange_1h) ? meanline_1h + meanrange_1h * (2 * math.asin(1) * innermult) : na
loband1_1h = mtf_1h and not na(meanline_1h) and not na(meanrange_1h) ? meanline_1h - meanrange_1h * (2 * math.asin(1) * innermult) : na
// SRBR 1小时数据 - 使用单独函数获取每个值
supportLevel_1h = mtf_1h ? request.security(syminfo.tickerid, "60", getSupportLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
resistanceLevel_1h = mtf_1h ? request.security(syminfo.tickerid, "60", getResistanceLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
brekout_res_1h = mtf_1h ? request.security(syminfo.tickerid, "60", getBrekoutRes(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
res_holds_1h = mtf_1h ? request.security(syminfo.tickerid, "60", getResHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
sup_holds_1h = mtf_1h ? request.security(syminfo.tickerid, "60", getSupHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
brekout_sup_1h = mtf_1h ? request.security(syminfo.tickerid, "60", getBrekoutSup(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false

// 4小时数据
rsi_4h = mtf_4h ? get_mtf_rsi("240") : na
close_4h = mtf_4h ? request.security(syminfo.tickerid, "240", close, lookahead=barmerge.lookahead_off) : na
ma50_4h = mtf_4h ? request.security(syminfo.tickerid, "240", exponential ? ta.ema(close, 50) : ta.sma(close, 50), lookahead=barmerge.lookahead_off) : na
ma200_4h = mtf_4h ? request.security(syminfo.tickerid, "240", exponential ? ta.ema(close, 200) : ta.sma(close, 200), lookahead=barmerge.lookahead_off) : na
meanline_4h = mtf_4h ? request.security(syminfo.tickerid, "240", supersmoother(source, length), lookahead=barmerge.lookahead_off) : na
meanrange_4h = mtf_4h ? request.security(syminfo.tickerid, "240", supersmoother(ta.tr, length), lookahead=barmerge.lookahead_off) : na
upband1_4h = mtf_4h and not na(meanline_4h) and not na(meanrange_4h) ? meanline_4h + meanrange_4h * (2 * math.asin(1) * innermult) : na
loband1_4h = mtf_4h and not na(meanline_4h) and not na(meanrange_4h) ? meanline_4h - meanrange_4h * (2 * math.asin(1) * innermult) : na
// SRBR 4小时数据 - 使用单独函数获取每个值
supportLevel_4h = mtf_4h ? request.security(syminfo.tickerid, "240", getSupportLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
resistanceLevel_4h = mtf_4h ? request.security(syminfo.tickerid, "240", getResistanceLevel(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : na
brekout_res_4h = mtf_4h ? request.security(syminfo.tickerid, "240", getBrekoutRes(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
res_holds_4h = mtf_4h ? request.security(syminfo.tickerid, "240", getResHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
sup_holds_4h = mtf_4h ? request.security(syminfo.tickerid, "240", getSupHolds(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false
brekout_sup_4h = mtf_4h ? request.security(syminfo.tickerid, "240", getBrekoutSup(close, lookbackPeriod), lookahead=barmerge.lookahead_off) : false

// 计算多时间框架信号（修正版本，与原指标逻辑一致）
// 1分钟信号
distance_1m = mtf_1m and not na(close_1m) and not na(ma200_1m) ? math.abs(close_1m - ma200_1m) : na
distance_threshold_1m = get_distance_threshold()

// 1分钟R1/S1/MEAN距离计算
distance_to_r1_1m = mtf_1m and not na(close_1m) and not na(upband1_1m) ? math.abs(close_1m - upband1_1m) : na
distance_to_s1_1m = mtf_1m and not na(close_1m) and not na(loband1_1m) ? math.abs(close_1m - loband1_1m) : na
distance_to_mean_1m = mtf_1m and not na(close_1m) and not na(meanline_1m) ? math.abs(close_1m - meanline_1m) : na

// 1分钟完整信号判断
rsi_neutral_1m = mtf_1m and not na(rsi_1m) ? (rsi_1m >= short_S and rsi_1m <= long_S) : false
price_in_channel_1m = mtf_1m and not na(close_1m) and not na(upband1_1m) and not na(loband1_1m) ? (close_1m >= loband1_1m and close_1m <= upband1_1m) : false
ma200_near_1m = mtf_1m and not na(distance_1m) ? (distance_1m <= distance_threshold_1m) : false
osc_1m = rsi_neutral_1m or price_in_channel_1m or ma200_near_1m

rsi_overbought_1m = mtf_1m and not na(rsi_1m) ? (rsi_1m > long_S) : false
price_above_r1_1m = mtf_1m and not na(close_1m) and not na(upband1_1m) ? (close_1m > upband1_1m) : false
ma200_far_1m = mtf_1m and not na(distance_1m) ? (distance_1m > distance_threshold_1m) : false
ma50_above_mean_1m = mtf_1m and not na(ma50_1m) and not na(meanline_1m) ? (ma50_1m > meanline_1m) : false
long_1m = rsi_overbought_1m and price_above_r1_1m and ma200_far_1m and ma50_above_mean_1m

rsi_oversold_1m = mtf_1m and not na(rsi_1m) ? (rsi_1m < short_S) : false
price_below_s1_1m = mtf_1m and not na(close_1m) and not na(loband1_1m) ? (close_1m < loband1_1m) : false
ma50_below_mean_1m = mtf_1m and not na(ma50_1m) and not na(meanline_1m) ? (ma50_1m < meanline_1m) : false
short_1m = rsi_oversold_1m and price_below_s1_1m and ma200_far_1m and ma50_below_mean_1m

// 1分钟MRC信号汇总
mrc_signal_1m = osc_1m ? "震荡" : long_1m ? "做多" : short_1m ? "做空" : "观望"

// 1分钟SRBR信号计算
srbr_signal_1m = get_price_status_srbr(close_1m, supportLevel_1m, resistanceLevel_1m)

// 1分钟综合信号
combined_signal_1m = get_combined_signal(mrc_signal_1m, srbr_signal_1m)
signal_strength_1m = get_signal_strength(mrc_signal_1m, srbr_signal_1m)

// 5分钟信号
distance_5m = mtf_5m and not na(close_5m) and not na(ma200_5m) ? math.abs(close_5m - ma200_5m) : na

// 5分钟R1/S1/MEAN距离计算
distance_to_r1_5m = mtf_5m and not na(close_5m) and not na(upband1_5m) ? math.abs(close_5m - upband1_5m) : na
distance_to_s1_5m = mtf_5m and not na(close_5m) and not na(loband1_5m) ? math.abs(close_5m - loband1_5m) : na
distance_to_mean_5m = mtf_5m and not na(close_5m) and not na(meanline_5m) ? math.abs(close_5m - meanline_5m) : na

rsi_neutral_5m = mtf_5m and not na(rsi_5m) ? (rsi_5m >= short_S and rsi_5m <= long_S) : false
price_in_channel_5m = mtf_5m and not na(close_5m) and not na(upband1_5m) and not na(loband1_5m) ? (close_5m >= loband1_5m and close_5m <= upband1_5m) : false
ma200_near_5m = mtf_5m and not na(distance_5m) ? (distance_5m <= distance_threshold_1m) : false
osc_5m = rsi_neutral_5m or price_in_channel_5m or ma200_near_5m

rsi_overbought_5m = mtf_5m and not na(rsi_5m) ? (rsi_5m > long_S) : false
price_above_r1_5m = mtf_5m and not na(close_5m) and not na(upband1_5m) ? (close_5m > upband1_5m) : false
price_above_mean_5m = mtf_5m and not na(close_5m) and not na(meanline_5m) ? (close_5m >= meanline_5m or math.abs(close_5m - meanline_5m) <= get_r1s1_distance_threshold()) : false
ma200_far_5m = mtf_5m and not na(distance_5m) ? (distance_5m > distance_threshold_1m) : false
ma50_above_mean_5m = mtf_5m and not na(ma50_5m) and not na(meanline_5m) ? (ma50_5m > meanline_5m) : false
long_5m = rsi_overbought_5m and price_above_r1_5m and ma200_far_5m and ma50_above_mean_5m

rsi_oversold_5m = mtf_5m and not na(rsi_5m) ? (rsi_5m < short_S) : false
price_below_s1_5m = mtf_5m and not na(close_5m) and not na(loband1_5m) ? (close_5m < loband1_5m) : false
price_below_mean_5m = mtf_5m and not na(close_5m) and not na(meanline_5m) ? (close_5m <= meanline_5m or math.abs(close_5m - meanline_5m) <= get_r1s1_distance_threshold()) : false
ma50_below_mean_5m = mtf_5m and not na(ma50_5m) and not na(meanline_5m) ? (ma50_5m < meanline_5m) : false
short_5m = rsi_oversold_5m and price_below_s1_5m and ma200_far_5m and ma50_below_mean_5m

// 5分钟MRC信号汇总
mrc_signal_5m = osc_5m ? "震荡" : long_5m ? "做多" : short_5m ? "做空" : "观望"

// 5分钟SRBR信号计算
srbr_signal_5m = get_price_status_srbr(close_5m, supportLevel_5m, resistanceLevel_5m)

// 5分钟综合信号
combined_signal_5m = get_combined_signal(mrc_signal_5m, srbr_signal_5m)
signal_strength_5m = get_signal_strength(mrc_signal_5m, srbr_signal_5m)

// 15分钟信号
distance_15m = mtf_15m and not na(close_15m) and not na(ma200_15m) ? math.abs(close_15m - ma200_15m) : na

// 15分钟R1/S1/MEAN距离计算
distance_to_r1_15m = mtf_15m and not na(close_15m) and not na(upband1_15m) ? math.abs(close_15m - upband1_15m) : na
distance_to_s1_15m = mtf_15m and not na(close_15m) and not na(loband1_15m) ? math.abs(close_15m - loband1_15m) : na
distance_to_mean_15m = mtf_15m and not na(close_15m) and not na(meanline_15m) ? math.abs(close_15m - meanline_15m) : na

rsi_neutral_15m = mtf_15m and not na(rsi_15m) ? (rsi_15m >= short_S and rsi_15m <= long_S) : false
price_in_channel_15m = mtf_15m and not na(close_15m) and not na(upband1_15m) and not na(loband1_15m) ? (close_15m >= loband1_15m and close_15m <= upband1_15m) : false
ma200_near_15m = mtf_15m and not na(distance_15m) ? (distance_15m <= distance_threshold_1m) : false
osc_15m = rsi_neutral_15m or price_in_channel_15m or ma200_near_15m

rsi_overbought_15m = mtf_15m and not na(rsi_15m) ? (rsi_15m > long_S) : false
price_above_r1_15m = mtf_15m and not na(close_15m) and not na(upband1_15m) ? (close_15m > upband1_15m) : false
ma200_far_15m = mtf_15m and not na(distance_15m) ? (distance_15m > distance_threshold_1m) : false
ma50_above_mean_15m = mtf_15m and not na(ma50_15m) and not na(meanline_15m) ? (ma50_15m > meanline_15m) : false
long_15m = rsi_overbought_15m and price_above_r1_15m and ma200_far_15m and ma50_above_mean_15m

rsi_oversold_15m = mtf_15m and not na(rsi_15m) ? (rsi_15m < short_S) : false
price_below_s1_15m = mtf_15m and not na(close_15m) and not na(loband1_15m) ? (close_15m < loband1_15m) : false
ma50_below_mean_15m = mtf_15m and not na(ma50_15m) and not na(meanline_15m) ? (ma50_15m < meanline_15m) : false
short_15m = rsi_oversold_15m and price_below_s1_15m and ma200_far_15m and ma50_below_mean_15m

// 15分钟MRC信号汇总
mrc_signal_15m = osc_15m ? "震荡" : long_15m ? "做多" : short_15m ? "做空" : "观望"

// 15分钟SRBR信号计算
srbr_signal_15m = get_price_status_srbr(close_15m, supportLevel_15m, resistanceLevel_15m)

// 15分钟综合信号
combined_signal_15m = get_combined_signal(mrc_signal_15m, srbr_signal_15m)
signal_strength_15m = get_signal_strength(mrc_signal_15m, srbr_signal_15m)

// 30分钟信号
distance_30m = mtf_30m and not na(close_30m) and not na(ma200_30m) ? math.abs(close_30m - ma200_30m) : na

// 30分钟R1/S1/MEAN距离计算
distance_to_r1_30m = mtf_30m and not na(close_30m) and not na(upband1_30m) ? math.abs(close_30m - upband1_30m) : na
distance_to_s1_30m = mtf_30m and not na(close_30m) and not na(loband1_30m) ? math.abs(close_30m - loband1_30m) : na
distance_to_mean_30m = mtf_30m and not na(close_30m) and not na(meanline_30m) ? math.abs(close_30m - meanline_30m) : na

rsi_neutral_30m = mtf_30m and not na(rsi_30m) ? (rsi_30m >= short_S and rsi_30m <= long_S) : false
price_in_channel_30m = mtf_30m and not na(close_30m) and not na(upband1_30m) and not na(loband1_30m) ? (close_30m >= loband1_30m and close_30m <= upband1_30m) : false
ma200_near_30m = mtf_30m and not na(distance_30m) ? (distance_30m <= distance_threshold_1m) : false
osc_30m = rsi_neutral_30m or price_in_channel_30m or ma200_near_30m

rsi_overbought_30m = mtf_30m and not na(rsi_30m) ? (rsi_30m > long_S) : false
price_above_r1_30m = mtf_30m and not na(close_30m) and not na(upband1_30m) ? (close_30m > upband1_30m) : false
ma200_far_30m = mtf_30m and not na(distance_30m) ? (distance_30m > distance_threshold_1m) : false
ma50_above_mean_30m = mtf_30m and not na(ma50_30m) and not na(meanline_30m) ? (ma50_30m > meanline_30m) : false
long_30m = rsi_overbought_30m and price_above_r1_30m and ma200_far_30m and ma50_above_mean_30m

rsi_oversold_30m = mtf_30m and not na(rsi_30m) ? (rsi_30m < short_S) : false
price_below_s1_30m = mtf_30m and not na(close_30m) and not na(loband1_30m) ? (close_30m < loband1_30m) : false
ma50_below_mean_30m = mtf_30m and not na(ma50_30m) and not na(meanline_30m) ? (ma50_30m < meanline_30m) : false
short_30m = rsi_oversold_30m and price_below_s1_30m and ma200_far_30m and ma50_below_mean_30m

// 30分钟MRC信号汇总
mrc_signal_30m = osc_30m ? "震荡" : long_30m ? "做多" : short_30m ? "做空" : "观望"

// 30分钟SRBR信号计算
srbr_signal_30m = get_price_status_srbr(close_30m, supportLevel_30m, resistanceLevel_30m)

// 30分钟综合信号
combined_signal_30m = get_combined_signal(mrc_signal_30m, srbr_signal_30m)
signal_strength_30m = get_signal_strength(mrc_signal_30m, srbr_signal_30m)

// 45分钟信号
distance_45m = mtf_45m and not na(close_45m) and not na(ma200_45m) ? math.abs(close_45m - ma200_45m) : na

// 45分钟R1/S1/MEAN距离计算
distance_to_r1_45m = mtf_45m and not na(close_45m) and not na(upband1_45m) ? math.abs(close_45m - upband1_45m) : na
distance_to_s1_45m = mtf_45m and not na(close_45m) and not na(loband1_45m) ? math.abs(close_45m - loband1_45m) : na
distance_to_mean_45m = mtf_45m and not na(close_45m) and not na(meanline_45m) ? math.abs(close_45m - meanline_45m) : na

rsi_neutral_45m = mtf_45m and not na(rsi_45m) ? (rsi_45m >= short_S and rsi_45m <= long_S) : false
price_in_channel_45m = mtf_45m and not na(close_45m) and not na(upband1_45m) and not na(loband1_45m) ? (close_45m >= loband1_45m and close_45m <= upband1_45m) : false
ma200_near_45m = mtf_45m and not na(distance_45m) ? (distance_45m <= distance_threshold_1m) : false
osc_45m = rsi_neutral_45m or price_in_channel_45m or ma200_near_45m

rsi_overbought_45m = mtf_45m and not na(rsi_45m) ? (rsi_45m > long_S) : false
price_above_r1_45m = mtf_45m and not na(close_45m) and not na(upband1_45m) ? (close_45m > upband1_45m) : false
ma200_far_45m = mtf_45m and not na(distance_45m) ? (distance_45m > distance_threshold_1m) : false
ma50_above_mean_45m = mtf_45m and not na(ma50_45m) and not na(meanline_45m) ? (ma50_45m > meanline_45m) : false
long_45m = rsi_overbought_45m and price_above_r1_45m and ma200_far_45m and ma50_above_mean_45m

rsi_oversold_45m = mtf_45m and not na(rsi_45m) ? (rsi_45m < short_S) : false
price_below_s1_45m = mtf_45m and not na(close_45m) and not na(loband1_45m) ? (close_45m < loband1_45m) : false
ma50_below_mean_45m = mtf_45m and not na(ma50_45m) and not na(meanline_45m) ? (ma50_45m < meanline_45m) : false
short_45m = rsi_oversold_45m and price_below_s1_45m and ma200_far_45m and ma50_below_mean_45m

// 45分钟MRC信号汇总
mrc_signal_45m = osc_45m ? "震荡" : long_45m ? "做多" : short_45m ? "做空" : "观望"

// 45分钟SRBR信号计算
srbr_signal_45m = get_price_status_srbr(close_45m, supportLevel_45m, resistanceLevel_45m)

// 45分钟综合信号
combined_signal_45m = get_combined_signal(mrc_signal_45m, srbr_signal_45m)
signal_strength_45m = get_signal_strength(mrc_signal_45m, srbr_signal_45m)

// 1小时信号
distance_1h = mtf_1h and not na(close_1h) and not na(ma200_1h) ? math.abs(close_1h - ma200_1h) : na

// 1小时R1/S1/MEAN距离计算
distance_to_r1_1h = mtf_1h and not na(close_1h) and not na(upband1_1h) ? math.abs(close_1h - upband1_1h) : na
distance_to_s1_1h = mtf_1h and not na(close_1h) and not na(loband1_1h) ? math.abs(close_1h - loband1_1h) : na
distance_to_mean_1h = mtf_1h and not na(close_1h) and not na(meanline_1h) ? math.abs(close_1h - meanline_1h) : na

rsi_neutral_1h = mtf_1h and not na(rsi_1h) ? (rsi_1h >= short_S and rsi_1h <= long_S) : false
price_in_channel_1h = mtf_1h and not na(close_1h) and not na(upband1_1h) and not na(loband1_1h) ? (close_1h >= loband1_1h and close_1h <= upband1_1h) : false
ma200_near_1h = mtf_1h and not na(distance_1h) ? (distance_1h <= distance_threshold_1m) : false
osc_1h = rsi_neutral_1h or price_in_channel_1h or ma200_near_1h

rsi_overbought_1h = mtf_1h and not na(rsi_1h) ? (rsi_1h > long_S) : false
price_above_r1_1h = mtf_1h and not na(close_1h) and not na(upband1_1h) ? (close_1h > upband1_1h) : false
ma200_far_1h = mtf_1h and not na(distance_1h) ? (distance_1h > distance_threshold_1m) : false
ma50_above_mean_1h = mtf_1h and not na(ma50_1h) and not na(meanline_1h) ? (ma50_1h > meanline_1h) : false
long_1h = rsi_overbought_1h and price_above_r1_1h and ma200_far_1h and ma50_above_mean_1h

rsi_oversold_1h = mtf_1h and not na(rsi_1h) ? (rsi_1h < short_S) : false
price_below_s1_1h = mtf_1h and not na(close_1h) and not na(loband1_1h) ? (close_1h < loband1_1h) : false
ma50_below_mean_1h = mtf_1h and not na(ma50_1h) and not na(meanline_1h) ? (ma50_1h < meanline_1h) : false
short_1h = rsi_oversold_1h and price_below_s1_1h and ma200_far_1h and ma50_below_mean_1h

// 1小时MRC信号汇总
mrc_signal_1h = osc_1h ? "震荡" : long_1h ? "做多" : short_1h ? "做空" : "观望"

// 1小时SRBR信号计算
srbr_signal_1h = get_price_status_srbr(close_1h, supportLevel_1h, resistanceLevel_1h)

// 1小时综合信号
combined_signal_1h = get_combined_signal(mrc_signal_1h, srbr_signal_1h)
signal_strength_1h = get_signal_strength(mrc_signal_1h, srbr_signal_1h)

// 4小时信号
distance_4h = mtf_4h and not na(close_4h) and not na(ma200_4h) ? math.abs(close_4h - ma200_4h) : na

// 4小时R1/S1/MEAN距离计算
distance_to_r1_4h = mtf_4h and not na(close_4h) and not na(upband1_4h) ? math.abs(close_4h - upband1_4h) : na
distance_to_s1_4h = mtf_4h and not na(close_4h) and not na(loband1_4h) ? math.abs(close_4h - loband1_4h) : na
distance_to_mean_4h = mtf_4h and not na(close_4h) and not na(meanline_4h) ? math.abs(close_4h - meanline_4h) : na

rsi_neutral_4h = mtf_4h and not na(rsi_4h) ? (rsi_4h >= short_S and rsi_4h <= long_S) : false
price_in_channel_4h = mtf_4h and not na(close_4h) and not na(upband1_4h) and not na(loband1_4h) ? (close_4h >= loband1_4h and close_4h <= upband1_4h) : false
ma200_near_4h = mtf_4h and not na(distance_4h) ? (distance_4h <= distance_threshold_1m) : false
osc_4h = rsi_neutral_4h or price_in_channel_4h or ma200_near_4h

rsi_overbought_4h = mtf_4h and not na(rsi_4h) ? (rsi_4h > long_S) : false
price_above_r1_4h = mtf_4h and not na(close_4h) and not na(upband1_4h) ? (close_4h > upband1_4h) : false
ma200_far_4h = mtf_4h and not na(distance_4h) ? (distance_4h > distance_threshold_1m) : false
ma50_above_mean_4h = mtf_4h and not na(ma50_4h) and not na(meanline_4h) ? (ma50_4h > meanline_4h) : false
long_4h = rsi_overbought_4h and price_above_r1_4h and ma200_far_4h and ma50_above_mean_4h

rsi_oversold_4h = mtf_4h and not na(rsi_4h) ? (rsi_4h < short_S) : false
price_below_s1_4h = mtf_4h and not na(close_4h) and not na(loband1_4h) ? (close_4h < loband1_4h) : false
ma50_below_mean_4h = mtf_4h and not na(ma50_4h) and not na(meanline_4h) ? (ma50_4h < meanline_4h) : false
short_4h = rsi_oversold_4h and price_below_s1_4h and ma200_far_4h and ma50_below_mean_4h

// 4小时MRC信号汇总
mrc_signal_4h = osc_4h ? "震荡" : long_4h ? "做多" : short_4h ? "做空" : "观望"

// 4小时SRBR信号计算
srbr_signal_4h = get_price_status_srbr(close_4h, supportLevel_4h, resistanceLevel_4h)

// 4小时综合信号
combined_signal_4h = get_combined_signal(mrc_signal_4h, srbr_signal_4h)
signal_strength_4h = get_signal_strength(mrc_signal_4h, srbr_signal_4h)

//************************************************************************************************************
// 计算实时数值
//************************************************************************************************************

// MA50 和 MEAN 的关系
ma50_vs_mean = ma50 > meanline ? "上方" : ma50 < meanline ? "下方" : "重合"
ma50_mean_distance = math.abs(ma50 - meanline)

// MA50 和 MA200 的关系
ma50_vs_ma200 = ma50 > ma200 ? "上方" : ma50 < ma200 ? "下方" : "重合"
ma50_ma200_distance = math.abs(ma50 - ma200)

// 价格和 MA200 的关系
price_vs_ma200 = close > ma200 ? "上方" : close < ma200 ? "下方" : "重合"
price_ma200_distance = math.abs(close - ma200)

// MA200 历史值
ma200_current = ma200
ma200_4bars_ago = ma200[4]
ma200_10bars_ago = ma200[10]
ma200_14bars_ago = ma200[14]

// MA200 斜率计算
ma200_slope_4 = not na(ma200_4bars_ago) ? (ma200_current - ma200_4bars_ago) / 4 : 0
ma200_slope_10 = not na(ma200_10bars_ago) ? (ma200_current - ma200_10bars_ago) / 10 : 0
ma200_slope_14 = not na(ma200_14bars_ago) ? (ma200_10bars_ago - ma200_14bars_ago) / 4 : 0

// MA200 斜率变化计算
ma200_slope_change_4 = not na(ma200_slope_4[1]) ? ma200_slope_4 - ma200_slope_4[1] : 0
ma200_slope_change_10 = not na(ma200_slope_10[1]) ? ma200_slope_10 - ma200_slope_10[1] : 0

// 删除不再使用的角度计算

// 保留原有斜率计算（用于其他功能）
ma200_slope_segment1 = not na(ma200_4bars_ago) ? (ma200_current - ma200_4bars_ago) / 4 : 0
ma200_slope_segment2 = not na(ma200_14bars_ago) and not na(ma200_10bars_ago) ? (ma200_10bars_ago - ma200_14bars_ago) / 4 : 0

// 删除不再使用的角度和百分比相关代码

// ═════════ MA200距离分析（参考原MA200代码） ════════
distance_threshold = get_distance_threshold()
distance_usd = math.abs(close - ma200_current)
is_near_ma200 = distance_usd <= distance_threshold

// ═════════ R1/S1距离分析 ════════
r1s1_distance_threshold = get_r1s1_distance_threshold()
distance_to_r1 = math.abs(close - upband1)
distance_to_s1 = math.abs(close - loband1)
is_near_r1 = distance_to_r1 <= r1s1_distance_threshold
is_near_s1 = distance_to_s1 <= r1s1_distance_threshold

// MA200平坦度分析
ma200_max = ta.highest(ma200_current, flatness_period)
ma200_min = ta.lowest(ma200_current, flatness_period)
ma200_flatness = ma200_max - ma200_min
is_flat = ma200_flatness < flatness_threshold

// 价格波动分析
price_max = ta.highest(close, oscillation_bars)
price_min = ta.lowest(close, oscillation_bars)
price_range = price_max - price_min
cross_above = ta.crossover(close, ma200_current)
cross_below = ta.crossunder(close, ma200_current)
is_oscillating_price = price_range < oscillation_threshold and (cross_above or cross_below)

// 删除重复的百分比计算（已在前面定义）

// 删除残留的旧代码片段

// 删除不再使用的斜率变化方向计算

// ═════════ 基于距离的MA200分析逻辑（参考原MA200代码） ════════

// 1. 基础状态判断
is_above_ma200 = close > ma200_current
is_below_ma200 = close < ma200_current

// 2. MA200趋势方向判断（基于MA200自身变化）
ma200_change_4 = not na(ma200_4bars_ago) ? ma200_current - ma200_4bars_ago : 0
ma200_change_10 = not na(ma200_10bars_ago) ? ma200_current - ma200_10bars_ago : 0

ma200_direction = ma200_change_10 > flatness_threshold ? "上升" : ma200_change_10 < -flatness_threshold ? "下降" : "横盘"

// 3. 趋势强度判断（基于MA200变化幅度）
ma200_change_abs = math.abs(ma200_change_10)
ma200_strength = ma200_change_abs >= flatness_threshold * 4 ? "强" : ma200_change_abs >= flatness_threshold * 2 ? "中" : ma200_change_abs >= flatness_threshold ? "弱" : "微弱"

// 4. 振荡判断（参考原MA200逻辑）
// 条件1：MA200本身比较平坦
condition1_ma200_flat = is_flat

// 条件2：价格在MA200附近波动
condition2_price_oscillating = is_oscillating_price

// 条件3：价格靠近MA200
condition3_near_ma200 = is_near_ma200

// 综合振荡判断
is_oscillating = condition1_ma200_flat or condition2_price_oscillating or (condition3_near_ma200 and ma200_direction == "横盘")

// 5. 最终趋势判断
ma200_trend_strength = is_oscillating ? "震荡" : ma200_strength + "趋势"
ma200_trend_direction = is_oscillating ? "横盘" : ma200_direction

// 6. 趋势变化分析（基于距离变化）
distance_change = not na(distance_usd[1]) ? distance_usd - distance_usd[1] : 0
trend_acceleration = math.abs(distance_change) > distance_threshold * 0.1 ? (distance_change > 0 ? "远离" : "靠近") : "稳定"

// 综合趋势状态
ma200_trend_status = ma200_trend_direction + ma200_trend_strength

// 市场活跃度判断（基于距离和波动）
volatility_indicator = distance_usd + price_range
market_activity = volatility_indicator > distance_threshold * 2 ? "活跃" : volatility_indicator > distance_threshold ? "一般" : "平静"

// ═════════ 交易信号判断系统 ════════

// 1. 震荡条件判断（修改为OR逻辑：满足任一条件即为震荡）
rsi_neutral = rsi >= short_S and rsi <= long_S  // RSI处于中性区间
price_in_channel = close >= loband1 and close <= upband1  // 价格在MRC通道内（S1-R1）
ma200_near = distance_usd <= distance_threshold  // 距离MA200小于等于阈值

oscillation_signal = rsi_neutral or price_in_channel or ma200_near

// 2. 做多条件判断
rsi_overbought_signal = rsi > long_S  // RSI超买
price_above_r1 = close > upband1  // 价格在R1以上
ma200_far = distance_usd > distance_threshold  // 距离MA200大于阈值
ma50_above_mean = ma50 > meanline  // MA50在MEAN以上

long_signal = rsi_overbought_signal and price_above_r1 and ma200_far and ma50_above_mean

// 3. 做空条件判断
rsi_oversold_signal = rsi < short_S  // RSI超卖
price_below_s1 = close < loband1  // 价格在S1以下
ma50_below_mean = ma50 < meanline  // MA50在MEAN以下

short_signal = rsi_oversold_signal and price_below_s1 and ma200_far and ma50_below_mean

// ═════════ 时间窗口警报系统 ════════

// 存储启动条件的时间戳和冷却时间
var int long_trigger_time = na
var int short_trigger_time = na
var int last_long_alert_time = na
var int last_short_alert_time = na

// 1分钟时间周期数据获取（用于启动条件检测）
close_1m_current = request.security(syminfo.tickerid, "1", close, lookahead=barmerge.lookahead_off)
close_1m_prev = request.security(syminfo.tickerid, "1", close[1], lookahead=barmerge.lookahead_off)
upband1_1m_current = request.security(syminfo.tickerid, "1", upband1, lookahead=barmerge.lookahead_off)
loband1_1m_current = request.security(syminfo.tickerid, "1", loband1, lookahead=barmerge.lookahead_off)

// 获取成交量过滤结果
[long_volume_ok, short_volume_ok] = get_volume_filter()

// 成交量趋势确认（在全局作用域调用以避免警告）
long_volume_trend = volume_trend_confirmation("long")
short_volume_trend = volume_trend_confirmation("short")
neutral_volume_trend = volume_trend_confirmation("neutral")

// 成交量分析数据
current_delta_volume = upAndDownVolume()
volume_abs = math.abs(current_delta_volume)
volume_avg = ta.sma(volume_abs, volume_confirmation_bars)
volume_strength_ratio = volume_avg > 0 ? volume_abs / volume_avg : 0
volume_is_strong = volume_strength_ratio > volume_strength_multiplier

// 启动条件检测（加入成交量过滤）
long_trigger_price = ta.crossover(close_1m_current, upband1_1m_current)  // 价格上穿R1
short_trigger_price = ta.crossunder(close_1m_current, loband1_1m_current)  // 价格下穿S1

// 结合成交量过滤的启动条件（分层过滤）
// 第一层：价格突破条件
long_trigger_basic = long_trigger_price
short_trigger_basic = short_trigger_price

// 第二层：成交量确认条件（可选应用到启动条件）
// 用户可以选择是否在启动时就应用成交量过滤，或者只在后续条件中应用
long_trigger = (enable_volume_filter and apply_volume_to_trigger) ? (long_trigger_basic and long_volume_ok) : long_trigger_basic
short_trigger = (enable_volume_filter and apply_volume_to_trigger) ? (short_trigger_basic and short_volume_ok) : short_trigger_basic

// 记录启动时间
if long_trigger
    long_trigger_time := time
if short_trigger
    short_trigger_time := time

// 时间窗口检查函数
in_time_window(trigger_time, window_minutes) =>
    if na(trigger_time)
        false
    else
        time_diff = math.abs(time - trigger_time) / 60000  // 转换为分钟
        time_diff <= window_minutes

// 冷却时间检查函数
is_cooldown_over(last_alert_time, cooldown_minutes) =>
    if na(last_alert_time)
        true
    else
        time_diff = (time - last_alert_time) / 60000  // 转换为分钟
        time_diff >= cooldown_minutes

// 做多条件检查（时间窗口内，加入成交量确认）
check_long_conditions() =>
    if not enable_advanced_alerts
        false
    else
        // 成交量确认（使用预先计算的结果）
        volume_confirm = enable_volume_filter ? long_volume_trend : true

        // 条件2（1分钟时间框架确认，5分钟时间窗口）：快速技术指标验证
        // - 时间窗口：启动信号后5分钟内必须满足所有子条件
        // - RSI超买确认：1分钟RSI > 超买阈值，确认短期多头动能
        // - 价格突破确认：1分钟价格突破R1阻力线，确认突破有效性
        // - MA200距离确认：1分钟价格距离MA200足够远，避免长期均线阻力
        // - MA50趋势确认：1分钟MA50在MEAN线上方，确认短期趋势一致性
        // - 成交量确认：成交量过滤通过，确保有足够的市场参与度
        condition2_1m = in_time_window(long_trigger_time, time_window_5min) and rsi_overbought_1m and price_above_r1_1m and ma200_far_1m and ma50_above_mean_1m and volume_confirm

        // 条件3（5分钟时间框架确认，15分钟时间窗口）：多重技术指标综合验证
        // - 时间窗口：启动信号后15分钟内必须满足所有子条件
        // - RSI超买确认：5分钟RSI > 超买阈值，确认多头动能充足
        // - 价格位置确认：5分钟价格突破R1阻力线，确认强势突破
        // - MA200距离确认：5分钟价格距离MA200足够远，避免长期均线阻力
        // - MA50趋势确认：1分钟MA50在MEAN线上方，确认短期趋势向上
        // - 成交量确认：成交量过滤通过，确保有足够的市场参与度
        condition3_5m = in_time_window(long_trigger_time, time_window_15min) and rsi_overbought_5m and price_above_r1_5m and ma200_far_5m and ma50_above_mean_1m and volume_confirm

        // 条件4（15分钟时间框架确认，10分钟时间窗口）：长期趋势距离验证
        // - 时间窗口：启动信号后10分钟内必须满足距离条件
        // - 距离确认：15分钟价格与S1支撑线距离 > 品种特定阈值
        // - 目的：确保价格远离重要支撑位，避免假突破风险
        // - 意义：长期时间框架的距离验证，提高信号可靠性
        condition4_15m = in_time_window(long_trigger_time, time_window_10min) and distance_to_s1_15m > get_r1s1_distance_threshold()

        // 根据配置决定是否需要所有条件都满足
        conditions_met = require_all_conditions ? (condition2_1m and condition3_5m and condition4_15m) : (condition2_1m or condition3_5m or condition4_15m)

        // 检查冷却时间
        cooldown_ok = is_cooldown_over(last_long_alert_time, alert_cooldown_minutes)

        conditions_met and cooldown_ok

// 做空条件检查（时间窗口内，加入成交量确认）
check_short_conditions() =>
    if not enable_advanced_alerts
        false
    else
        // 成交量确认（使用预先计算的结果）
        volume_confirm = enable_volume_filter ? short_volume_trend : true

        // 条件2（1分钟时间框架确认，5分钟时间窗口）：快速技术指标验证
        // - 时间窗口：启动信号后5分钟内必须满足所有子条件
        // - RSI超卖确认：1分钟RSI < 超卖阈值，确认短期空头动能
        // - 价格突破确认：1分钟价格跌破S1支撑线，确认突破有效性
        // - MA200距离确认：1分钟价格距离MA200足够远，避免长期均线支撑
        // - MA50趋势确认：1分钟MA50在MEAN线下方，确认短期趋势一致性
        // - 成交量确认：成交量过滤通过，确保有足够的市场参与度
        condition2_1m = in_time_window(short_trigger_time, time_window_5min) and rsi_oversold_1m and price_below_s1_1m and ma200_far_1m and ma50_below_mean_1m and volume_confirm

        // 条件3（5分钟时间框架确认，15分钟时间窗口）：多重技术指标综合验证
        // - 时间窗口：启动信号后15分钟内必须满足所有子条件
        // - RSI超卖确认：5分钟RSI < 超卖阈值，确认空头动能充足
        // - 价格位置确认：5分钟价格跌破S1支撑线，确认强势突破
        // - MA200距离确认：5分钟价格距离MA200足够远，避免长期均线支撑
        // - MA50趋势确认：1分钟MA50在MEAN线下方，确认短期趋势向下
        // - 成交量确认：成交量过滤通过，确保有足够的市场参与度
        condition3_5m = in_time_window(short_trigger_time, time_window_15min) and rsi_oversold_5m and price_below_s1_5m and ma200_far_5m and ma50_below_mean_1m and volume_confirm

        // 条件4（15分钟时间框架确认，10分钟时间窗口）：长期趋势距离验证
        // - 时间窗口：启动信号后10分钟内必须满足距离条件
        // - 距离确认：15分钟价格与R1阻力线距离 > 品种特定阈值
        // - 目的：确保价格远离重要阻力位，避免假突破风险
        // - 意义：长期时间框架的距离验证，提高信号可靠性
        condition4_15m = in_time_window(short_trigger_time, time_window_10min) and distance_to_r1_15m > get_r1s1_distance_threshold()

        // 根据配置决定是否需要所有条件都满足
        conditions_met = require_all_conditions ? (condition2_1m and condition3_5m and condition4_15m) : (condition2_1m or condition3_5m or condition4_15m)

        // 检查冷却时间
        cooldown_ok = is_cooldown_over(last_short_alert_time, alert_cooldown_minutes)

        conditions_met and cooldown_ok

// 最终警报条件
advanced_long_alert = check_long_conditions()
advanced_short_alert = check_short_conditions()

// 更新最后警报时间
if advanced_long_alert
    last_long_alert_time := time
if advanced_short_alert
    last_short_alert_time := time

// ═════════ 警报系统（包含原有和新增） ════════

// 传统警报条件（使用常量消息）
alertcondition(oscillation_signal, title="震荡信号", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"震荡信号","信号":"oscillation","时间":"{{time}}","描述":"震荡"}')

alertcondition(long_signal, title="做多信号", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"做多信号","信号":"long","时间":"{{time}}","描述":"做多信号"}')

alertcondition(short_signal, title="做空信号", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"做空信号","信号":"short","时间":"{{time}}","描述":"做空信号"}')

// 新增时间窗口警报
alertcondition(advanced_long_alert, title="高级做多警报", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"高级做多警报","信号":"advanced_long","时间":"{{time}}","描述":"多时间周期做多确认信号"}')

alertcondition(advanced_short_alert, title="高级做空警报", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"高级做空警报","信号":"advanced_short","时间":"{{time}}","描述":"多时间周期做空确认信号"}')

// R1/S1距离警报
alertcondition(is_near_r1, title="价格接近R1线", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"价格接近R1线","信号":"near_r1","时间":"{{time}}","描述":"价格接近R1阻力线"}')

alertcondition(is_near_s1, title="价格接近S1线", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"价格接近S1线","信号":"near_s1","时间":"{{time}}","描述":"价格接近S1支撑线"}')

// 成交量相关警报
alertcondition(long_volume_ok and enable_volume_filter, title="做多成交量确认", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"做多成交量确认","信号":"long_volume","时间":"{{time}}","描述":"检测到强劲买盘成交量"}')

alertcondition(short_volume_ok and enable_volume_filter, title="做空成交量确认", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"做空成交量确认","信号":"short_volume","时间":"{{time}}","描述":"检测到强劲卖盘成交量"}')

// ═════════ 策略交易警报 ═════════
// 策略开仓警报
alertcondition(enable_strategy and position_opened and barstate.isconfirmed, title="策略开仓", message='{"action":"OPEN","strategy":"MRC+MA200+RSI+srbr8.1","symbol":"{{ticker}}","timeframe":"{{interval}}","price":"{{close}}","time":"{{time}}","reason":"综合信号触发"}')

// 策略平仓警报
alertcondition(enable_strategy and not position_opened and barstate.isconfirmed, title="策略平仓", message='{"action":"CLOSE","strategy":"MRC+MA200+RSI+srbr8.1","symbol":"{{ticker}}","timeframe":"{{interval}}","price":"{{close}}","time":"{{time}}","reason":"策略平仓条件触发"}')

// 信号延续警报
alertcondition(enable_strategy and position_opened, title="信号延续", message='{"action":"HOLD","strategy":"MRC+MA200+RSI+srbr8.1","symbol":"{{ticker}}","timeframe":"{{interval}}","price":"{{close}}","time":"{{time}}","reason":"同方向信号延续"}')

// ═════════ 新增：MRC+SRBR综合信号警报 ════════
// 综合警报开关与触发方式
enable_combined_alerts = input.bool(true, title='启用综合信号警报', group='综合信号标记')
alerts_only_on_close = input.bool(false, title='仅收盘触发综合警报', group='综合信号标记')
enable_debug_alerts = input.bool(true, title='启用调试警报（简化版）', group='综合信号标记')

// MA过滤开关
enable_4h_ma200_filter = input.bool(false, title='启用4小时MA200位置过滤', group='综合信号标记', tooltip='开启后，综合信号需要实时价格与4小时MA200位置关系符合条件')
enable_4h_ma50_filter = input.bool(true, title='启用4小时MA50位置过滤', group='综合信号标记', tooltip='开启后，综合信号需要实时价格与4小时MA50位置关系符合条件')


// 计算当前图表时间周期的MRC信号
current_mrc_signal = oscillation_signal ? "震荡" : long_signal ? "做多" : short_signal ? "做空" : "观望"

// 计算当前图表时间周期的SRBR信号
current_srbr_signal = get_price_status_srbr(close, supportLevel, resistanceLevel)

// 1小时时间周期实时价格与mean线关系检查
price_above_mean_1h = mtf_1h and not na(close) and not na(meanline_1h) ? (close > meanline_1h) : true  // 使用实时价格与1小时MEAN线比较
price_below_mean_1h = mtf_1h and not na(close) and not na(meanline_1h) ? (close < meanline_1h) : true  // 使用实时价格与1小时MEAN线比较

// 5分钟时间周期实时价格与R1/S1线关系检查
price_above_r1_5m_check = mtf_5m and not na(close) and not na(upband1_5m) ? (close > upband1_5m) : true  // 使用实时价格与5分钟R1线比较
price_below_s1_5m_check = mtf_5m and not na(close) and not na(loband1_5m) ? (close < loband1_5m) : true  // 使用实时价格与5分钟S1线比较

// ═════════ 新增：多时间周期实时价格与MA50位置关系过滤 ════════
// 做多时实时价格需要在各时间周期MA50以上，做空时实时价格需要在各时间周期MA50以下
price_above_ma50_1m = mtf_1m and not na(close) and not na(ma50_1m) ? (close > ma50_1m) : true
price_above_ma50_5m = mtf_5m and not na(close) and not na(ma50_5m) ? (close > ma50_5m) : true
price_above_ma50_15m = mtf_15m and not na(close) and not na(ma50_15m) ? (close > ma50_15m) : true
// price_above_ma50_30m = mtf_30m and not na(close) and not na(ma50_30m) ? (close > ma50_30m) : true  // 暂时注释30分钟MA50
price_above_ma50_1h = mtf_1h and not na(close) and not na(ma50_1h) ? (close > ma50_1h) : true
price_above_ma50_4h = enable_4h_ma50_filter and mtf_4h and not na(close) and not na(ma50_4h) ? (close > ma50_4h) : true  // 可控制的4小时MA50

price_below_ma50_1m = mtf_1m and not na(close) and not na(ma50_1m) ? (close < ma50_1m) : true
price_below_ma50_5m = mtf_5m and not na(close) and not na(ma50_5m) ? (close < ma50_5m) : true
price_below_ma50_15m = mtf_15m and not na(close) and not na(ma50_15m) ? (close < ma50_15m) : true
// price_below_ma50_30m = mtf_30m and not na(close) and not na(ma50_30m) ? (close < ma50_30m) : true  // 暂时注释30分钟MA50
price_below_ma50_1h = mtf_1h and not na(close) and not na(ma50_1h) ? (close < ma50_1h) : true
price_below_ma50_4h = enable_4h_ma50_filter and mtf_4h and not na(close) and not na(ma50_4h) ? (close < ma50_4h) : true  // 可控制的4小时MA50

// 所有时间周期的MA50位置关系过滤（包含1分钟，4小时根据开关决定是否包含）
all_ma50_above_filters_pass = price_above_ma50_1m and price_above_ma50_5m and price_above_ma50_15m and price_above_ma50_1h and price_above_ma50_4h // and price_above_ma50_30m
all_ma50_below_filters_pass = price_below_ma50_1m and price_below_ma50_5m and price_below_ma50_15m and price_below_ma50_1h and price_below_ma50_4h // and price_below_ma50_30m

// ═════════ 新增：实时价格与多时间周期MA200位置关系过滤 ════════
// 做多时实时价格需要在各时间周期MA200以上，做空时实时价格需要在各时间周期MA200以下
price_above_ma200_1m = mtf_1m and not na(close) and not na(ma200_1m) ? (close > ma200_1m) : true
price_above_ma200_5m = mtf_5m and not na(close) and not na(ma200_5m) ? (close > ma200_5m) : true
price_above_ma200_15m = mtf_15m and not na(close) and not na(ma200_15m) ? (close > ma200_15m) : true
price_above_ma200_1h = mtf_1h and not na(close) and not na(ma200_1h) ? (close > ma200_1h) : true
price_above_ma200_4h = enable_4h_ma200_filter and mtf_4h and not na(close) and not na(ma200_4h) ? (close > ma200_4h) : true  // 可控制的4小时MA200

price_below_ma200_1m = mtf_1m and not na(close) and not na(ma200_1m) ? (close < ma200_1m) : true
price_below_ma200_5m = mtf_5m and not na(close) and not na(ma200_5m) ? (close < ma200_5m) : true
price_below_ma200_15m = mtf_15m and not na(close) and not na(ma200_15m) ? (close < ma200_15m) : true
price_below_ma200_1h = mtf_1h and not na(close) and not na(ma200_1h) ? (close < ma200_1h) : true
price_below_ma200_4h = enable_4h_ma200_filter and mtf_4h and not na(close) and not na(ma200_4h) ? (close < ma200_4h) : true  // 可控制的4小时MA200

// 所有时间周期的MA200位置关系过滤（4小时根据开关决定是否包含）
all_ma200_above_filters_pass = price_above_ma200_1m and price_above_ma200_5m and price_above_ma200_15m and price_above_ma200_1h and price_above_ma200_4h
all_ma200_below_filters_pass = price_below_ma200_1m and price_below_ma200_5m and price_below_ma200_15m and price_below_ma200_1h and price_below_ma200_4h

// ═════════ 暂时禁用：多时间周期MA距离过滤 ════════
// 所有时间周期的MA200距离过滤都必须通过（暂时禁用，默认通过）
all_ma_distance_filters_pass = true

// ═════════ 暂时禁用：多时间周期MA50-MA200区间过滤 ════════
// 所有时间周期的MA50-MA200区间过滤都必须通过（暂时禁用，默认通过）
all_ma_range_filters_pass = true

// 综合信号一致性判断（增加新的多时间周期MA位置过滤条件）
mrc_srbr_both_bullish = (current_mrc_signal == "做多" and current_srbr_signal == "看多" and price_above_mean_1h and price_above_r1_5m_check and all_ma_distance_filters_pass and all_ma_range_filters_pass and all_ma50_above_filters_pass and all_ma200_above_filters_pass)
mrc_srbr_both_bearish = (current_mrc_signal == "做空" and current_srbr_signal == "看空" and price_below_mean_1h and price_below_s1_5m_check and all_ma_distance_filters_pass and all_ma_range_filters_pass and all_ma50_below_filters_pass and all_ma200_below_filters_pass)

// 综合做多警报：当MRC做多且SRBR看多时（移除成交量过滤，与显示标记条件保持一致）
alertcondition(enable_combined_alerts and mrc_srbr_both_bullish and (alerts_only_on_close ? barstate.isconfirmed : true), title="综合做多信号", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"综合做多信号","信号":"combined_long","MRC信号":"做多","SRBR信号":"看多","时间":"{{time}}","描述":"MRC和SRBR信号方向一致，确认做多"}')

// 综合做空警报：当MRC做空且SRBR看空时（移除成交量过滤，与显示标记条件保持一致）
alertcondition(enable_combined_alerts and mrc_srbr_both_bearish and (alerts_only_on_close ? barstate.isconfirmed : true), title="综合做空信号", message='{"指标名称":"cobinined","交易对":"{{ticker}}","周期":"{{interval}}","价格":"{{close}}","事件":"综合做空信号","信号":"combined_short","MRC信号":"做空","SRBR信号":"看空","时间":"{{time}}","描述":"MRC和SRBR信号方向一致，确认做空"}')



// ═════════ 新增：MRC+SRBR综合信号K线标记 ════════
// 专门为综合信号添加明显的K线标记

// 综合信号K线标记配置
show_combined_signals = input.bool(true, title='显示综合信号标记', group='综合信号标记')
signal_size = input.string('large', title='标记大小', options=['tiny', 'small', 'normal', 'large', 'huge'], group='综合信号标记')
signal_offset = input.int(0, title='标记偏移', minval=-100, maxval=100, group='综合信号标记')

// 获取标记大小
get_signal_size() =>
    switch signal_size
        'tiny' => size.tiny
        'small' => size.small
        'normal' => size.normal
        'large' => size.large
        'huge' => size.huge
        => size.large

// 综合做多信号标记
if show_combined_signals and mrc_srbr_both_bullish
    label.new(bar_index, low - (high - low) * 0.1, "🚀做多", style=label.style_label_up, color=color.new(color.lime, 0), textcolor=color.white, size=get_signal_size(), tooltip="MRC+SRBR综合做多信号\\nMRC信号: " + current_mrc_signal + "\\nSRBR信号: " + current_srbr_signal + "\\n当前价格: " + str.tostring(close, '#.##'))

// 综合做空信号标记
if show_combined_signals and mrc_srbr_both_bearish
    label.new(bar_index, high + (high - low) * 0.1, "🔻做空", style=label.style_label_down, color=color.new(color.fuchsia, 0), textcolor=color.white, size=get_signal_size(), tooltip="MRC+SRBR综合做空信号\\nMRC信号: " + current_mrc_signal + "\\nSRBR信号: " + current_srbr_signal + "\\n当前价格: " + str.tostring(close, '#.##'))

// 在K线上绘制醒目的背景高亮
bgcolor(mrc_srbr_both_bullish and show_combined_signals ? color.new(color.lime, 90) : mrc_srbr_both_bearish and show_combined_signals ? color.new(color.fuchsia, 90) : na)

// 在价格线上绘制醒目的标记点
plotshape(mrc_srbr_both_bullish and show_combined_signals, title="综合做多点", style=shape.triangleup, location=location.belowbar, color=color.lime, size=size.large)
plotshape(mrc_srbr_both_bearish and show_combined_signals, title="综合做空点", style=shape.triangledown, location=location.abovebar, color=color.fuchsia, size=size.large)

// 额外的醒目标记 - 大圆点已移除（避免粗横条显示）
// plotshape(mrc_srbr_both_bullish and show_combined_signals, title="综合做多圆点", style=shape.circle, location=location.absolute, color=color.new(color.lime, 30), size=size.huge)
// plotshape(mrc_srbr_both_bearish and show_combined_signals, title="综合做空圆点", style=shape.circle, location=location.absolute, color=color.new(color.fuchsia, 30), size=size.huge)

// 在成交量区域也添加标记（如果有成交量显示）
plotshape(mrc_srbr_both_bullish and show_combined_signals, title="综合做多量能标记", style=shape.arrowup, location=location.bottom, color=color.lime, size=size.small)
plotshape(mrc_srbr_both_bearish and show_combined_signals, title="综合做空量能标记", style=shape.arrowdown, location=location.bottom, color=color.fuchsia, size=size.small)

// 可选：在信号价格位置绘制水平参考线
show_signal_lines = input.bool(false, title='显示信号价格线', group='综合信号标记')
signal_line_extend = input.string('right', title='价格线延伸方向', options=['left', 'right', 'both', 'none'], group='综合信号标记')

// 获取线条延伸方式
get_line_extend() =>
    switch signal_line_extend
        'left' => extend.left
        'right' => extend.right
        'both' => extend.both
        'none' => extend.none
        => extend.right

// 绘制综合做多信号价格线
if show_signal_lines and show_combined_signals and mrc_srbr_both_bullish
    line.new(bar_index, close, bar_index + 10, close, xloc=xloc.bar_index, extend=get_line_extend(), color=color.new(color.lime, 50), width=2, style=line.style_dashed)

// 绘制综合做空信号价格线
if show_signal_lines and show_combined_signals and mrc_srbr_both_bearish
    line.new(bar_index, close, bar_index + 10, close, xloc=xloc.bar_index, extend=get_line_extend(), color=color.new(color.fuchsia, 50), width=2, style=line.style_dashed)

// RSI 状态（保留用于表格显示）
rsi_position = rsi > long_S ? "超买区" : rsi < short_S ? "超卖区" : "中性区"
rsi_cross_count = math.abs(segment_count)

//************************************************************************************************************
// 所有绘图功能已移除，只保留多时间周期表格显示
//************************************************************************************************************

//************************************************************************************************************
// 信息表格显示
//************************************************************************************************************

if show_info_table and barstate.islast
    // 创建扩展的信息表格（包含高级警报状态、成交量信息和策略状态）
    var table info_table = table.new(
         position = position.bottom_right,  // 固定在右下角
         columns = 14,  // 扩展为14列：原6列 + 高级警报4列 + 成交量2列 + 策略2列
         rows = 4,      // 4行：标题 + 数值 + 状态 + 信号
         bgcolor = color.new(color.white, 85),
         border_width = 1)

    // 清空表格
    table.clear(info_table, 0, 0, 13, 3)

    // 获取配置的文字大小
    text_size = get_text_size()

    // ═════════ 扩展表格标题行（原6列 + 高级警报4列 + 成交量2列） ═════════
    table.cell(info_table, 0, 0, "RSI", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(info_table, 1, 0, "价格位置", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(info_table, 2, 0, "MA50位置", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(info_table, 3, 0, "MA200距离", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(info_table, 4, 0, "信号汇总", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(info_table, 5, 0, "综合判断", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    // 高级警报相关列
    table.cell(info_table, 6, 0, "高级启动", text_color=color.white, bgcolor=color.new(color.lime, 30), text_size=text_size)
    table.cell(info_table, 7, 0, "条件2(1m)", text_color=color.white, bgcolor=color.new(color.lime, 30), text_size=text_size)
    table.cell(info_table, 8, 0, "条件3(5m)", text_color=color.white, bgcolor=color.new(color.lime, 30), text_size=text_size)
    table.cell(info_table, 9, 0, "条件4(15m)", text_color=color.white, bgcolor=color.new(color.lime, 30), text_size=text_size)
    // 成交量相关列
    table.cell(info_table, 10, 0, "成交量", text_color=color.white, bgcolor=color.new(color.blue, 30), text_size=text_size)
    table.cell(info_table, 11, 0, "量能确认", text_color=color.white, bgcolor=color.new(color.blue, 30), text_size=text_size)
    // 策略相关列
    table.cell(info_table, 12, 0, "策略状态", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(info_table, 13, 0, "持仓信息", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)

    // ═════════ 多列紧凑数据 ═════════
    // 计算各指标的背景颜色（类似combined_mrc_ma200_rsi.pine）
    rsi_color = rsi_neutral ? color.new(color.orange, 40) : rsi_overbought_signal ? color.new(color.green, 40) : rsi_oversold_signal ? color.new(color.red, 40) : color.white
    price_color = price_in_channel ? color.new(color.orange, 40) : price_above_r1 ? color.new(color.green, 40) : price_below_s1 ? color.new(color.red, 40) : color.white
    ma50_color = ma50_above_mean ? color.new(color.green, 40) : ma50_below_mean ? color.new(color.red, 40) : color.white
    ma200_color = ma200_near ? color.new(color.orange, 40) : color.new(color.gray, 60)

    // 高级警报条件状态计算
    condition2_1m_long = not na(long_trigger_time) and in_time_window(long_trigger_time, time_window_5min) and rsi_overbought_1m and price_above_r1_1m and ma200_far_1m and ma50_above_mean_1m
    condition3_5m_long = not na(long_trigger_time) and in_time_window(long_trigger_time, time_window_15min) and rsi_overbought_5m and price_above_r1_5m and ma200_far_5m and ma50_above_mean_1m
    condition4_15m_long = not na(long_trigger_time) and in_time_window(long_trigger_time, time_window_10min) and distance_to_s1_15m > get_r1s1_distance_threshold()

    condition2_1m_short = not na(short_trigger_time) and in_time_window(short_trigger_time, time_window_5min) and rsi_oversold_1m and price_below_s1_1m and ma200_far_1m and ma50_below_mean_1m
    condition3_5m_short = not na(short_trigger_time) and in_time_window(short_trigger_time, time_window_15min) and rsi_oversold_5m and price_below_s1_5m and ma200_far_5m and ma50_below_mean_1m
    condition4_15m_short = not na(short_trigger_time) and in_time_window(short_trigger_time, time_window_10min) and distance_to_r1_15m > get_r1s1_distance_threshold()

    // 高级警报颜色计算
    trigger_color = (long_trigger or short_trigger) ? color.new(color.aqua, 40) : (not na(long_trigger_time) or not na(short_trigger_time)) ? color.new(color.yellow, 60) : color.new(color.gray, 80)
    condition2_color = condition2_1m_long ? color.new(color.green, 40) : condition2_1m_short ? color.new(color.red, 40) : color.new(color.gray, 80)
    condition3_color = condition3_5m_long ? color.new(color.green, 40) : condition3_5m_short ? color.new(color.red, 40) : color.new(color.gray, 80)
    condition4_color = condition4_15m_long ? color.new(color.green, 40) : condition4_15m_short ? color.new(color.red, 40) : color.new(color.gray, 80)

    // 综合信号颜色（包含高级警报）
    signal_color = advanced_long_alert ? color.new(color.lime, 20) : advanced_short_alert ? color.new(color.fuchsia, 20) : oscillation_signal ? color.new(color.orange, 40) : long_signal ? color.new(color.green, 40) : short_signal ? color.new(color.red, 40) : color.new(color.gray, 80)
    final_signal_color = advanced_long_alert ? color.new(color.lime, 20) : advanced_short_alert ? color.new(color.fuchsia, 20) : long_signal ? color.new(color.green, 30) : short_signal ? color.new(color.red, 30) : oscillation_signal ? color.new(color.orange, 30) : color.new(color.gray, 80)

    // 计算成交量相关数据
    current_volume = current_delta_volume
    volume_trend = neutral_volume_trend
    volume_color = current_volume > 0 ? color.new(color.green, 40) : current_volume < 0 ? color.new(color.red, 40) : color.new(color.gray, 60)
    volume_confirm_color = volume_is_strong ? color.new(color.lime, 40) : color.new(color.gray, 80)

    // 第1行：数值
    table.cell(info_table, 0, 1, str.tostring(rsi, '#.#'), text_color=color.black, bgcolor=rsi_color, text_size=text_size)
    table.cell(info_table, 1, 1, str.tostring(close, '#.##'), text_color=color.black, bgcolor=price_color, text_size=text_size)
    table.cell(info_table, 2, 1, str.tostring(ma50, '#.##'), text_color=color.black, bgcolor=ma50_color, text_size=text_size)
    table.cell(info_table, 3, 1, str.tostring(distance_usd, '#.#'), text_color=color.black, bgcolor=ma200_color, text_size=text_size)
    table.cell(info_table, 4, 1, advanced_long_alert ? "高级🚀" : advanced_short_alert ? "高级🔻" : oscillation_signal ? "震荡⚡" : long_signal ? "做多🚀" : short_signal ? "做空🔻" : "观望", text_color=color.black, bgcolor=signal_color, text_size=text_size)
    table.cell(info_table, 5, 1, mrc_srbr_both_bullish ? "🚀综合做多" : mrc_srbr_both_bearish ? "🔻综合做空" : advanced_long_alert ? "✓高级做多" : advanced_short_alert ? "✓高级做空" : long_signal ? "✓做多" : short_signal ? "✓做空" : oscillation_signal ? "✓震荡" : "观望", text_color=color.black, bgcolor=mrc_srbr_both_bullish ? color.new(color.lime, 10) : mrc_srbr_both_bearish ? color.new(color.fuchsia, 10) : final_signal_color, text_size=text_size)
    // 高级警报数据
    table.cell(info_table, 6, 1, long_trigger ? "🚀启动" : short_trigger ? "🔻启动" : not na(long_trigger_time) ? "🚀监控" : not na(short_trigger_time) ? "🔻监控" : "等待", text_color=color.black, bgcolor=trigger_color, text_size=text_size)
    table.cell(info_table, 7, 1, condition2_1m_long ? "✓做多" : condition2_1m_short ? "✓做空" : "未满足", text_color=color.black, bgcolor=condition2_color, text_size=text_size)
    table.cell(info_table, 8, 1, condition3_5m_long ? "✓做多" : condition3_5m_short ? "✓做空" : "未满足", text_color=color.black, bgcolor=condition3_color, text_size=text_size)
    table.cell(info_table, 9, 1, condition4_15m_long ? "✓做多" : condition4_15m_short ? "✓做空" : "未满足", text_color=color.black, bgcolor=condition4_color, text_size=text_size)
    // 成交量数据
    table.cell(info_table, 10, 1, str.tostring(current_volume, '#.##'), text_color=color.black, bgcolor=volume_color, text_size=text_size)
    table.cell(info_table, 11, 1, volume_trend ? "✓确认" : "待确认", text_color=color.black, bgcolor=volume_confirm_color, text_size=text_size)
    // 策略数据
    strategy_status = enable_strategy ? (position_opened ? "持仓中" : "等待信号") : "已禁用"
    strategy_color = enable_strategy ? (position_opened ? color.new(color.lime, 40) : color.new(color.yellow, 60)) : color.new(color.gray, 80)
    position_info = position_opened ? current_position_direction + "@" + str.tostring(entry_price, '#.##') : "无持仓"
    position_color = position_opened ? (current_position_direction == "LONG" ? color.new(color.green, 40) : color.new(color.red, 40)) : color.new(color.gray, 80)
    table.cell(info_table, 12, 1, strategy_status, text_color=color.black, bgcolor=strategy_color, text_size=text_size)
    table.cell(info_table, 13, 1, position_info, text_color=color.black, bgcolor=position_color, text_size=text_size)

    // 第2行：状态
    table.cell(info_table, 0, 2, rsi_neutral ? "中性" : rsi_overbought_signal ? "超买" : rsi_oversold_signal ? "超卖" : "正常", text_color=color.black, bgcolor=rsi_color, text_size=text_size)
    table.cell(info_table, 1, 2, price_in_channel ? "通道内" : price_above_r1 ? "R1上" : price_below_s1 ? "S1下" : "其他", text_color=color.black, bgcolor=price_color, text_size=text_size)
    table.cell(info_table, 2, 2, ma50_above_mean ? "MEAN上" : ma50_below_mean ? "MEAN下" : "MEAN附近", text_color=color.black, bgcolor=ma50_color, text_size=text_size)
    table.cell(info_table, 3, 2, ma200_near ? "≤阈值" : ">阈值", text_color=color.black, bgcolor=ma200_color, text_size=text_size)
    table.cell(info_table, 4, 2, advanced_long_alert ? "高级AND" : advanced_short_alert ? "高级AND" : oscillation_signal ? "OR逻辑" : long_signal ? "AND逻辑" : short_signal ? "AND逻辑" : "无信号", text_color=color.black, bgcolor=signal_color, text_size=text_size)
    table.cell(info_table, 5, 2, mrc_srbr_both_bullish ? "🚀🎯" : mrc_srbr_both_bearish ? "🔻🎯" : advanced_long_alert ? "🚀" : advanced_short_alert ? "🔻" : long_signal ? "🚀" : short_signal ? "🔻" : oscillation_signal ? "⚡" : "⚪", text_color=color.black, bgcolor=mrc_srbr_both_bullish ? color.new(color.lime, 10) : mrc_srbr_both_bearish ? color.new(color.fuchsia, 10) : final_signal_color, text_size=text_size)
    // 高级警报状态
    trigger_window_status = not na(long_trigger_time) ? (in_time_window(long_trigger_time, time_window_15min) ? "窗口内" : "已过期") : not na(short_trigger_time) ? (in_time_window(short_trigger_time, time_window_15min) ? "窗口内" : "已过期") : "未启动"
    cooldown_status = is_cooldown_over(last_long_alert_time, alert_cooldown_minutes) and is_cooldown_over(last_short_alert_time, alert_cooldown_minutes) ? "可用" : "冷却中"
    table.cell(info_table, 6, 2, trigger_window_status, text_color=color.black, bgcolor=trigger_color, text_size=text_size)
    table.cell(info_table, 7, 2, condition2_1m_long ? "5分钟内" : condition2_1m_short ? "5分钟内" : "窗口外", text_color=color.black, bgcolor=condition2_color, text_size=text_size)
    table.cell(info_table, 8, 2, condition3_5m_long ? "15分钟内" : condition3_5m_short ? "15分钟内" : "窗口外", text_color=color.black, bgcolor=condition3_color, text_size=text_size)
    table.cell(info_table, 9, 2, condition4_15m_long ? "10分钟内" : condition4_15m_short ? "10分钟内" : "窗口外", text_color=color.black, bgcolor=condition4_color, text_size=text_size)
    // 成交量状态
    volume_status = current_volume > 0 ? "买盘" : current_volume < 0 ? "卖盘" : "平衡"
    volume_strength_text = enable_volume_filter ? (volume_is_strong ? "强势" : "弱势") + "|" + str.tostring(volume_filter_level) + "级" : "未启用"
    table.cell(info_table, 10, 2, volume_status, text_color=color.black, bgcolor=volume_color, text_size=text_size)
    table.cell(info_table, 11, 2, volume_strength_text, text_color=color.black, bgcolor=volume_confirm_color, text_size=text_size)
    // 策略状态
    signal_time_left = position_opened and not na(last_signal_time) ? math.max(0, signal_hold_minutes - (time - last_signal_time) / 60000) : 0
    strategy_status_text = enable_strategy ? (position_opened ? "剩余" + str.tostring(signal_time_left, '#.1') + "分钟" : "监控中") : "策略关闭"
    pnl_percent = position_opened and not na(entry_price) ? (close - entry_price) / entry_price * 100 * (current_position_direction == "LONG" ? 1 : -1) : 0
    pnl_text = position_opened ? (pnl_percent >= 0 ? "+" : "") + str.tostring(pnl_percent, '#.2') + "%" : "无盈亏"
    pnl_color = position_opened ? (pnl_percent >= 0 ? color.new(color.green, 40) : color.new(color.red, 40)) : color.new(color.gray, 80)
    table.cell(info_table, 12, 2, strategy_status_text, text_color=color.black, bgcolor=strategy_color, text_size=text_size)
    table.cell(info_table, 13, 2, pnl_text, text_color=color.black, bgcolor=pnl_color, text_size=text_size)

    // 第3行：条件说明
    table.cell(info_table, 0, 3, rsi_neutral ? "震荡条件" : rsi_overbought_signal ? "做多条件" : rsi_oversold_signal ? "做空条件" : "中性", text_color=color.black, bgcolor=rsi_color, text_size=text_size)
    table.cell(info_table, 1, 3, price_in_channel ? "震荡条件" : price_above_r1 ? "做多条件" : price_below_s1 ? "做空条件" : "中性", text_color=color.black, bgcolor=price_color, text_size=text_size)
    table.cell(info_table, 2, 3, ma50_above_mean ? "做多条件" : ma50_below_mean ? "做空条件" : "中性", text_color=color.black, bgcolor=ma50_color, text_size=text_size)
    table.cell(info_table, 3, 3, ma200_near ? "震荡条件" : "趋势条件", text_color=color.black, bgcolor=ma200_color, text_size=text_size)
    table.cell(info_table, 4, 3, advanced_long_alert ? "高级做多" : advanced_short_alert ? "高级做空" : oscillation_signal ? "满足震荡" : long_signal ? "满足做多" : short_signal ? "满足做空" : "无满足", text_color=color.black, bgcolor=signal_color, text_size=text_size)
    table.cell(info_table, 5, 3, mrc_srbr_both_bullish ? "MRC+SRBR双确认做多" : mrc_srbr_both_bearish ? "MRC+SRBR双确认做空" : advanced_long_alert ? "执行高级做多" : advanced_short_alert ? "执行高级做空" : long_signal ? "执行做多" : short_signal ? "执行做空" : oscillation_signal ? "保持震荡" : "继续观望", text_color=color.black, bgcolor=mrc_srbr_both_bullish ? color.new(color.lime, 10) : mrc_srbr_both_bearish ? color.new(color.fuchsia, 10) : final_signal_color, text_size=text_size)
    // 高级警报条件说明和调试信息
    trigger_debug = "启动:" + (long_trigger ? "多" : short_trigger ? "空" : "无") + "|时间:" + (not na(long_trigger_time) ? "多" : not na(short_trigger_time) ? "空" : "无")
    table.cell(info_table, 6, 3, trigger_debug, text_color=color.black, bgcolor=trigger_color, text_size=text_size)
    table.cell(info_table, 7, 3, condition2_1m_long ? "1m做多就绪" : condition2_1m_short ? "1m做空就绪" : "1m条件未满足", text_color=color.black, bgcolor=condition2_color, text_size=text_size)
    table.cell(info_table, 8, 3, condition3_5m_long ? "5m做多就绪" : condition3_5m_short ? "5m做空就绪" : "5m条件未满足", text_color=color.black, bgcolor=condition3_color, text_size=text_size)
    table.cell(info_table, 9, 3, condition4_15m_long ? "15m距离满足" : condition4_15m_short ? "15m距离满足" : "15m距离不足", text_color=color.black, bgcolor=condition4_color, text_size=text_size)
    // 成交量调试信息（更新为1-5级显示）
    volume_level_text = str.tostring(volume_filter_level) + "级"
    volume_debug = "Vol:" + str.tostring(current_volume, '#.##') + "|等级:" + volume_level_text + "|过滤:" + (long_volume_ok ? "多✓" : short_volume_ok ? "空✓" : "✗")
    table.cell(info_table, 10, 3, volume_debug, text_color=color.black, bgcolor=volume_color, text_size=text_size)
    alert_debug = "高级:" + (advanced_long_alert ? "多" : advanced_short_alert ? "空" : "无") + "|冷却:" + (is_cooldown_over(last_long_alert_time, alert_cooldown_minutes) and is_cooldown_over(last_short_alert_time, alert_cooldown_minutes) ? "OK" : "NO")
    table.cell(info_table, 11, 3, alert_debug, text_color=color.black, bgcolor=volume_confirm_color, text_size=text_size)
    // 策略调试信息
    [signal_long_1m, signal_short_1m, signal_direction] = detect_1m_signal()
    strategy_debug = "信号:" + (not na(signal_direction) ? signal_direction : "无") + "|持仓:" + (position_opened ? "是" : "否") + "|大小:" + str.tostring(position_size_percent) + "%"
    last_signal_info = position_opened and not na(last_signal_time) ? "最后信号:" + str.format_time(last_signal_time, "HH:mm") : "无最后信号"
    table.cell(info_table, 12, 3, strategy_debug, text_color=color.black, bgcolor=strategy_color, text_size=text_size)
    table.cell(info_table, 13, 3, last_signal_info, text_color=color.black, bgcolor=position_color, text_size=text_size)

    // ═════════ 关键数值表格（右中位置，避免与主表格重叠） ═════════
    // 创建关键数值表格
    var table key_values_table = table.new(
         position = position.middle_right,  // 改为右中位置
         columns = 4,  // 4列：R1阻力 + S1支撑 + MEAN中线 + 距离阈值
         rows = 3,     // 3行：标题 + 数值 + 通道信息
         bgcolor = color.new(color.white, 85),
         border_width = 1)

    // 清空表格
    table.clear(key_values_table, 0, 0, 3, 2)

    // 关键数值标题行
    table.cell(key_values_table, 0, 0, "R1阻力", text_color=color.white, bgcolor=color.new(color.red, 40), text_size=text_size)
    table.cell(key_values_table, 1, 0, "S1支撑", text_color=color.white, bgcolor=color.new(color.green, 40), text_size=text_size)
    table.cell(key_values_table, 2, 0, "MEAN中线", text_color=color.white, bgcolor=color.new(color.blue, 40), text_size=text_size)
    table.cell(key_values_table, 3, 0, "距离阈值", text_color=color.white, bgcolor=color.new(color.gray, 40), text_size=text_size)

    // 关键数值数据行
    table.cell(key_values_table, 0, 1, str.tostring(upband1, '#.####'), text_color=color.black, bgcolor=color.new(color.red, 20), text_size=text_size)
    table.cell(key_values_table, 1, 1, str.tostring(loband1, '#.####'), text_color=color.black, bgcolor=color.new(color.green, 20), text_size=text_size)
    table.cell(key_values_table, 2, 1, str.tostring(meanline, '#.####'), text_color=color.black, bgcolor=color.new(color.blue, 20), text_size=text_size)
    table.cell(key_values_table, 3, 1, str.tostring(distance_threshold, '#.##'), text_color=color.black, bgcolor=color.new(color.gray, 20), text_size=text_size)

    // 通道距离信息行
    channel_width = upband1 - loband1  // R1-S1通道宽度
    r1_r2_distance = upband2 - upband1  // R1到R2距离
    s1_s2_distance = loband1 - loband2  // S1到S2距离

    table.cell(key_values_table, 0, 2, "R1-R2:" + str.tostring(r1_r2_distance, '#.##'), text_color=color.black, bgcolor=color.new(color.red, 10), text_size=text_size)
    table.cell(key_values_table, 1, 2, "S1-S2:" + str.tostring(s1_s2_distance, '#.##'), text_color=color.black, bgcolor=color.new(color.green, 10), text_size=text_size)
    table.cell(key_values_table, 2, 2, "通道宽度:" + str.tostring(channel_width, '#.##'), text_color=color.black, bgcolor=color.new(color.blue, 10), text_size=text_size)
    table.cell(key_values_table, 3, 2, "当前距离:" + str.tostring(distance_usd, '#.#'), text_color=color.black, bgcolor=color.new(color.gray, 10), text_size=text_size)

// ═════════ 策略状态表格（左上角） ═════════
if enable_strategy and barstate.islast
    // 创建策略状态表格
    var table strategy_table = table.new(
         position = position.top_left,
         columns = 2,
         rows = 8,
         bgcolor = color.new(color.white, 85),
         border_width = 1)

    // 清空表格
    table.clear(strategy_table, 0, 0, 1, 7)

    // 获取配置的文字大小
    text_size = get_text_size()

    // 策略状态标题
    table.cell(strategy_table, 0, 0, "策略状态", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(strategy_table, 1, 0, "实时信息", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)

    // 策略启用状态
    table.cell(strategy_table, 0, 1, "策略状态", text_color=color.black, text_size=text_size)
    strategy_status_text = enable_strategy ? "✓ 已启用" : "✗ 已禁用"
    strategy_status_color = enable_strategy ? color.new(color.green, 60) : color.new(color.red, 60)
    table.cell(strategy_table, 1, 1, strategy_status_text, text_color=color.black, bgcolor=strategy_status_color, text_size=text_size)

    // 当前持仓状态
    table.cell(strategy_table, 0, 2, "持仓状态", text_color=color.black, text_size=text_size)
    position_status_text = position_opened ? "✓ 持仓中" : "✗ 无持仓"
    position_status_color = position_opened ? color.new(color.lime, 60) : color.new(color.gray, 60)
    table.cell(strategy_table, 1, 2, position_status_text, text_color=color.black, bgcolor=position_status_color, text_size=text_size)

    // 持仓方向
    table.cell(strategy_table, 0, 3, "持仓方向", text_color=color.black, text_size=text_size)
    direction_text = position_opened ? current_position_direction : "无"
    direction_color = position_opened ? (current_position_direction == "LONG" ? color.new(color.green, 60) : color.new(color.red, 60)) : color.new(color.gray, 60)
    table.cell(strategy_table, 1, 3, direction_text, text_color=color.black, bgcolor=direction_color, text_size=text_size)

    // 入场价格
    table.cell(strategy_table, 0, 4, "入场价格", text_color=color.black, text_size=text_size)
    entry_text = position_opened and not na(entry_price) ? str.tostring(entry_price, '#.####') : "无"
    table.cell(strategy_table, 1, 4, entry_text, text_color=color.black, text_size=text_size)

    // 当前盈亏
    table.cell(strategy_table, 0, 5, "当前盈亏", text_color=color.black, text_size=text_size)
    pnl_text = "无"
    pnl_color = color.new(color.gray, 60)
    if position_opened and not na(entry_price)
        pnl_percent = (close - entry_price) / entry_price * 100 * (current_position_direction == "LONG" ? 1 : -1)
        pnl_text := (pnl_percent >= 0 ? "+" : "") + str.tostring(pnl_percent, '#.2') + "%"
        pnl_color := pnl_percent >= 0 ? color.new(color.green, 60) : color.new(color.red, 60)
    table.cell(strategy_table, 1, 5, pnl_text, text_color=color.black, bgcolor=pnl_color, text_size=text_size)

    // 信号剩余时间
    table.cell(strategy_table, 0, 6, "信号剩余", text_color=color.black, text_size=text_size)
    time_text = "无"
    time_color = color.new(color.gray, 60)
    if position_opened and not na(last_signal_time)
        time_left = math.max(0, signal_hold_minutes - (time - last_signal_time) / 60000)
        time_text := str.tostring(time_left, '#.1') + " 分钟"
        time_color := time_left > 1 ? color.new(color.green, 60) : color.new(color.orange, 60)
    table.cell(strategy_table, 1, 6, time_text, text_color=color.black, bgcolor=time_color, text_size=text_size)

    // 1分钟信号状态
    table.cell(strategy_table, 0, 7, "1分钟信号", text_color=color.black, text_size=text_size)
    [signal_long_1m, signal_short_1m, signal_direction] = detect_1m_signal()
    signal_text = not na(signal_direction) ? signal_direction : "无信号"
    signal_color = signal_direction == "LONG" ? color.new(color.green, 60) : signal_direction == "SHORT" ? color.new(color.red, 60) : color.new(color.gray, 60)
    table.cell(strategy_table, 1, 7, signal_text, text_color=color.black, bgcolor=signal_color, text_size=text_size)

// ═════════ 策略性能统计表格（右上角） ═════════
if enable_strategy and barstate.islast
    // 创建性能统计表格
    var table performance_table = table.new(
         position = position.top_right,
         columns = 2,
         rows = 6,
         bgcolor = color.new(color.white, 85),
         border_width = 1)

    // 清空表格
    table.clear(performance_table, 0, 0, 1, 5)

    // 获取配置的文字大小
    text_size = get_text_size()

    // 性能统计标题
    table.cell(performance_table, 0, 0, "策略性能", text_color=color.white, bgcolor=color.new(color.blue, 30), text_size=text_size)
    table.cell(performance_table, 1, 0, "统计数据", text_color=color.white, bgcolor=color.new(color.blue, 30), text_size=text_size)

    // 总权益
    table.cell(performance_table, 0, 1, "总权益", text_color=color.black, text_size=text_size)
    equity_text = str.tostring(strategy.equity, '#.##')
    table.cell(performance_table, 1, 1, equity_text, text_color=color.black, text_size=text_size)

    // 净利润
    table.cell(performance_table, 0, 2, "净利润", text_color=color.black, text_size=text_size)
    net_profit = strategy.netprofit
    profit_text = str.tostring(net_profit, '#.##')
    profit_color = net_profit >= 0 ? color.new(color.green, 60) : color.new(color.red, 60)
    table.cell(performance_table, 1, 2, profit_text, text_color=color.black, bgcolor=profit_color, text_size=text_size)

    // 总交易次数
    table.cell(performance_table, 0, 3, "总交易", text_color=color.black, text_size=text_size)
    total_trades = strategy.closedtrades
    trades_text = str.tostring(total_trades, '#')
    table.cell(performance_table, 1, 3, trades_text, text_color=color.black, text_size=text_size)

    // 胜率
    table.cell(performance_table, 0, 4, "胜率", text_color=color.black, text_size=text_size)
    win_rate = total_trades > 0 ? strategy.wintrades / total_trades * 100 : 0
    win_rate_text = str.tostring(win_rate, '#.1') + "%"
    win_rate_color = win_rate >= 50 ? color.new(color.green, 60) : color.new(color.red, 60)
    table.cell(performance_table, 1, 4, win_rate_text, text_color=color.black, bgcolor=win_rate_color, text_size=text_size)

    // 最大回撤
    table.cell(performance_table, 0, 5, "最大回撤", text_color=color.black, text_size=text_size)
    max_dd = strategy.max_drawdown
    dd_text = str.tostring(max_dd, '#.##')
    dd_color = math.abs(max_dd) > strategy.initial_capital * 0.1 ? color.new(color.red, 60) : color.new(color.green, 60)
    table.cell(performance_table, 1, 5, dd_text, text_color=color.black, bgcolor=dd_color, text_size=text_size)

// ═════════ 多时间框架表格显示（融合MRC+SRBR） ═════════
if show_mtf_table and barstate.islast
    // 创建融合后的多时间框架表格
    var table mtf_table = table.new(
         position = position.top_right,
         columns = mtf_table_columns,  // 使用配置的列数（14列）
         rows = mtf_table_rows,
         bgcolor = color.new(color.white, 85),
         border_width = 1)

    // 清空表格
    table.clear(mtf_table, 0, 0, mtf_table_columns-1, mtf_table_rows - 1)

    // 获取配置的文字大小
    text_size = get_text_size()

    // ═════════ 融合表格标题行（14列） ═════════
    table.cell(mtf_table, 0, 0, "时间周期", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(mtf_table, 1, 0, "当前价格", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(mtf_table, 2, 0, "RSI值", text_color=color.white, bgcolor=color.new(color.blue, 30), text_size=text_size)
    table.cell(mtf_table, 3, 0, "RSI状态", text_color=color.white, bgcolor=color.new(color.blue, 30), text_size=text_size)
    table.cell(mtf_table, 4, 0, "价格位置", text_color=color.white, bgcolor=color.new(color.blue, 30), text_size=text_size)
    table.cell(mtf_table, 5, 0, "MA200距离", text_color=color.white, bgcolor=color.new(color.blue, 30), text_size=text_size)
    table.cell(mtf_table, 6, 0, "MRC信号", text_color=color.white, bgcolor=color.new(color.blue, 30), text_size=text_size)
    table.cell(mtf_table, 7, 0, "支撑位", text_color=color.white, bgcolor=color.new(color.green, 30), text_size=text_size)
    table.cell(mtf_table, 8, 0, "阻力位", text_color=color.white, bgcolor=color.new(color.red, 30), text_size=text_size)
    table.cell(mtf_table, 9, 0, "距支撑", text_color=color.white, bgcolor=color.new(color.green, 30), text_size=text_size)
    table.cell(mtf_table, 10, 0, "距阻力", text_color=color.white, bgcolor=color.new(color.red, 30), text_size=text_size)
    table.cell(mtf_table, 11, 0, "SRBR信号", text_color=color.white, bgcolor=color.new(color.orange, 30), text_size=text_size)
    table.cell(mtf_table, 12, 0, "综合信号", text_color=color.white, bgcolor=color.new(color.maroon, 30), text_size=text_size)
    table.cell(mtf_table, 13, 0, "信号强度", text_color=color.white, bgcolor=color.new(color.maroon, 30), text_size=text_size)

    // ═════════ 1分钟时间框架数据（融合版本） ═════════
    if mtf_1m and not na(rsi_1m)
        // 计算SRBR距离
        [dist_to_sup_1m, dist_to_res_1m] = get_distance_to_levels_srbr(close_1m, supportLevel_1m, resistanceLevel_1m)

        // 获取各种状态和颜色
        rsi_status_1m = get_rsi_status(rsi_1m, long_S, short_S)
        rsi_color_1m = get_rsi_color(rsi_1m, long_S, short_S, rsi_neutral_1m, rsi_overbought_1m, rsi_oversold_1m)
        price_color_1m = get_price_color(close_1m, upband1_1m, loband1_1m, price_in_channel_1m, price_above_r1_1m, price_below_s1_1m)
        ma200_bg_color_1m = get_distance_bg_color(distance_1m, distance_threshold_1m)

        // MRC信号颜色
        mrc_signal_color_1m = get_signal_color(osc_1m, long_1m, short_1m)

        // SRBR信号颜色
        srbr_signal_color_1m = get_price_status_color_srbr(close_1m, supportLevel_1m, resistanceLevel_1m)

        // 综合信号颜色
        combined_signal_color_1m = get_combined_signal_color(combined_signal_1m)

        // 填充表格数据（14列）
        table.cell(mtf_table, 0, 1, "1分钟", text_color=color.black, bgcolor=color.new(color.gray, 80), text_size=text_size)
        table.cell(mtf_table, 1, 1, str.tostring(close_1m, '#.##'), text_color=color.black, bgcolor=color.white, text_size=text_size)
        table.cell(mtf_table, 2, 1, str.tostring(rsi_1m, '#.#'), text_color=color.black, bgcolor=rsi_color_1m, text_size=text_size)
        table.cell(mtf_table, 3, 1, rsi_status_1m, text_color=color.black, bgcolor=rsi_color_1m, text_size=text_size)
        table.cell(mtf_table, 4, 1, get_price_position(close_1m, upband1_1m, loband1_1m), text_color=color.black, bgcolor=price_color_1m, text_size=text_size)
        table.cell(mtf_table, 5, 1, str.tostring(distance_1m, '#.##'), text_color=color.black, bgcolor=ma200_bg_color_1m, text_size=text_size)
        table.cell(mtf_table, 6, 1, mrc_signal_1m, text_color=color.white, bgcolor=mrc_signal_color_1m, text_size=text_size)
        table.cell(mtf_table, 7, 1, str.tostring(supportLevel_1m, '#.##'), text_color=color.white, bgcolor=color.new(color.green, 60), text_size=text_size)
        table.cell(mtf_table, 8, 1, str.tostring(resistanceLevel_1m, '#.##'), text_color=color.white, bgcolor=color.new(color.red, 60), text_size=text_size)
        table.cell(mtf_table, 9, 1, str.tostring(dist_to_sup_1m, '#.##'), text_color=color.black, bgcolor=color.new(color.green, 80), text_size=text_size)
        table.cell(mtf_table, 10, 1, str.tostring(dist_to_res_1m, '#.##'), text_color=color.black, bgcolor=color.new(color.red, 80), text_size=text_size)
        table.cell(mtf_table, 11, 1, srbr_signal_1m, text_color=color.white, bgcolor=srbr_signal_color_1m, text_size=text_size)
        table.cell(mtf_table, 12, 1, combined_signal_1m, text_color=color.white, bgcolor=combined_signal_color_1m, text_size=text_size)
        table.cell(mtf_table, 13, 1, signal_strength_1m, text_color=color.white, bgcolor=combined_signal_color_1m, text_size=text_size)

    // ═════════ 5分钟时间框架数据（融合版本） ═════════
    if mtf_5m and not na(rsi_5m)
        // 计算SRBR距离
        [dist_to_sup_5m, dist_to_res_5m] = get_distance_to_levels_srbr(close_5m, supportLevel_5m, resistanceLevel_5m)

        // 获取各种状态和颜色
        rsi_status_5m = get_rsi_status(rsi_5m, long_S, short_S)
        rsi_color_5m = get_rsi_color(rsi_5m, long_S, short_S, rsi_neutral_5m, rsi_overbought_5m, rsi_oversold_5m)
        price_color_5m = get_price_color(close_5m, upband1_5m, loband1_5m, price_in_channel_5m, price_above_r1_5m, price_below_s1_5m)
        ma200_bg_color_5m = get_distance_bg_color(distance_5m, distance_threshold_1m)

        // MRC信号颜色
        mrc_signal_color_5m = get_signal_color(osc_5m, long_5m, short_5m)

        // SRBR信号颜色
        srbr_signal_color_5m = get_price_status_color_srbr(close_5m, supportLevel_5m, resistanceLevel_5m)

        // 综合信号颜色
        combined_signal_color_5m = get_combined_signal_color(combined_signal_5m)

        // 填充表格数据（14列）
        table.cell(mtf_table, 0, 2, "5分钟", text_color=color.black, bgcolor=color.new(color.gray, 80), text_size=text_size)
        table.cell(mtf_table, 1, 2, str.tostring(close_5m, '#.##'), text_color=color.black, bgcolor=color.white, text_size=text_size)
        table.cell(mtf_table, 2, 2, str.tostring(rsi_5m, '#.#'), text_color=color.black, bgcolor=rsi_color_5m, text_size=text_size)
        table.cell(mtf_table, 3, 2, rsi_status_5m, text_color=color.black, bgcolor=rsi_color_5m, text_size=text_size)
        table.cell(mtf_table, 4, 2, get_price_position(close_5m, upband1_5m, loband1_5m), text_color=color.black, bgcolor=price_color_5m, text_size=text_size)
        table.cell(mtf_table, 5, 2, str.tostring(distance_5m, '#.##'), text_color=color.black, bgcolor=ma200_bg_color_5m, text_size=text_size)
        table.cell(mtf_table, 6, 2, mrc_signal_5m, text_color=color.white, bgcolor=mrc_signal_color_5m, text_size=text_size)
        table.cell(mtf_table, 7, 2, str.tostring(supportLevel_5m, '#.##'), text_color=color.white, bgcolor=color.new(color.green, 60), text_size=text_size)
        table.cell(mtf_table, 8, 2, str.tostring(resistanceLevel_5m, '#.##'), text_color=color.white, bgcolor=color.new(color.red, 60), text_size=text_size)
        table.cell(mtf_table, 9, 2, str.tostring(dist_to_sup_5m, '#.##'), text_color=color.black, bgcolor=color.new(color.green, 80), text_size=text_size)
        table.cell(mtf_table, 10, 2, str.tostring(dist_to_res_5m, '#.##'), text_color=color.black, bgcolor=color.new(color.red, 80), text_size=text_size)
        table.cell(mtf_table, 11, 2, srbr_signal_5m, text_color=color.white, bgcolor=srbr_signal_color_5m, text_size=text_size)
        table.cell(mtf_table, 12, 2, combined_signal_5m, text_color=color.white, bgcolor=combined_signal_color_5m, text_size=text_size)
        table.cell(mtf_table, 13, 2, signal_strength_5m, text_color=color.white, bgcolor=combined_signal_color_5m, text_size=text_size)

    // ═════════ 15分钟时间框架数据（融合版本） ═════════
    if mtf_15m and not na(rsi_15m)
        // 计算SRBR距离
        [dist_to_sup_15m, dist_to_res_15m] = get_distance_to_levels_srbr(close_15m, supportLevel_15m, resistanceLevel_15m)

        // 获取各种状态和颜色
        rsi_status_15m = get_rsi_status(rsi_15m, long_S, short_S)
        rsi_color_15m = get_rsi_color(rsi_15m, long_S, short_S, rsi_neutral_15m, rsi_overbought_15m, rsi_oversold_15m)
        price_color_15m = get_price_color(close_15m, upband1_15m, loband1_15m, price_in_channel_15m, price_above_r1_15m, price_below_s1_15m)
        ma200_bg_color_15m = get_distance_bg_color(distance_15m, distance_threshold_1m)

        // MRC信号颜色
        mrc_signal_color_15m = get_signal_color(osc_15m, long_15m, short_15m)

        // SRBR信号颜色
        srbr_signal_color_15m = get_price_status_color_srbr(close_15m, supportLevel_15m, resistanceLevel_15m)

        // 综合信号颜色
        combined_signal_color_15m = get_combined_signal_color(combined_signal_15m)

        // 填充表格数据（14列）
        table.cell(mtf_table, 0, 3, "15分钟", text_color=color.black, bgcolor=color.new(color.gray, 80), text_size=text_size)
        table.cell(mtf_table, 1, 3, str.tostring(close_15m, '#.##'), text_color=color.black, bgcolor=color.white, text_size=text_size)
        table.cell(mtf_table, 2, 3, str.tostring(rsi_15m, '#.#'), text_color=color.black, bgcolor=rsi_color_15m, text_size=text_size)
        table.cell(mtf_table, 3, 3, rsi_status_15m, text_color=color.black, bgcolor=rsi_color_15m, text_size=text_size)
        table.cell(mtf_table, 4, 3, get_price_position(close_15m, upband1_15m, loband1_15m), text_color=color.black, bgcolor=price_color_15m, text_size=text_size)
        table.cell(mtf_table, 5, 3, str.tostring(distance_15m, '#.##'), text_color=color.black, bgcolor=ma200_bg_color_15m, text_size=text_size)
        table.cell(mtf_table, 6, 3, mrc_signal_15m, text_color=color.white, bgcolor=mrc_signal_color_15m, text_size=text_size)
        table.cell(mtf_table, 7, 3, str.tostring(supportLevel_15m, '#.##'), text_color=color.white, bgcolor=color.new(color.green, 60), text_size=text_size)
        table.cell(mtf_table, 8, 3, str.tostring(resistanceLevel_15m, '#.##'), text_color=color.white, bgcolor=color.new(color.red, 60), text_size=text_size)
        table.cell(mtf_table, 9, 3, str.tostring(dist_to_sup_15m, '#.##'), text_color=color.black, bgcolor=color.new(color.green, 80), text_size=text_size)
        table.cell(mtf_table, 10, 3, str.tostring(dist_to_res_15m, '#.##'), text_color=color.black, bgcolor=color.new(color.red, 80), text_size=text_size)
        table.cell(mtf_table, 11, 3, srbr_signal_15m, text_color=color.white, bgcolor=srbr_signal_color_15m, text_size=text_size)
        table.cell(mtf_table, 12, 3, combined_signal_15m, text_color=color.white, bgcolor=combined_signal_color_15m, text_size=text_size)
        table.cell(mtf_table, 13, 3, signal_strength_15m, text_color=color.white, bgcolor=combined_signal_color_15m, text_size=text_size)

    // ═════════ 30分钟时间框架数据（融合版本） ═════════
    if mtf_30m and not na(rsi_30m)
        // 计算SRBR距离
        [dist_to_sup_30m, dist_to_res_30m] = get_distance_to_levels_srbr(close_30m, supportLevel_30m, resistanceLevel_30m)

        // 获取各种状态和颜色
        rsi_status_30m = get_rsi_status(rsi_30m, long_S, short_S)
        rsi_color_30m = get_rsi_color(rsi_30m, long_S, short_S, rsi_neutral_30m, rsi_overbought_30m, rsi_oversold_30m)
        price_color_30m = get_price_color(close_30m, upband1_30m, loband1_30m, price_in_channel_30m, price_above_r1_30m, price_below_s1_30m)
        ma200_bg_color_30m = get_distance_bg_color(distance_30m, distance_threshold_1m)

        // MRC信号颜色
        mrc_signal_color_30m = get_signal_color(osc_30m, long_30m, short_30m)

        // SRBR信号颜色
        srbr_signal_color_30m = get_price_status_color_srbr(close_30m, supportLevel_30m, resistanceLevel_30m)

        // 综合信号颜色
        combined_signal_color_30m = get_combined_signal_color(combined_signal_30m)

        // 填充表格数据（14列）
        table.cell(mtf_table, 0, 4, "30分钟", text_color=color.black, bgcolor=color.new(color.gray, 80), text_size=text_size)
        table.cell(mtf_table, 1, 4, str.tostring(close_30m, '#.##'), text_color=color.black, bgcolor=color.white, text_size=text_size)
        table.cell(mtf_table, 2, 4, str.tostring(rsi_30m, '#.#'), text_color=color.black, bgcolor=rsi_color_30m, text_size=text_size)
        table.cell(mtf_table, 3, 4, rsi_status_30m, text_color=color.black, bgcolor=rsi_color_30m, text_size=text_size)
        table.cell(mtf_table, 4, 4, get_price_position(close_30m, upband1_30m, loband1_30m), text_color=color.black, bgcolor=price_color_30m, text_size=text_size)
        table.cell(mtf_table, 5, 4, str.tostring(distance_30m, '#.##'), text_color=color.black, bgcolor=ma200_bg_color_30m, text_size=text_size)
        table.cell(mtf_table, 6, 4, mrc_signal_30m, text_color=color.white, bgcolor=mrc_signal_color_30m, text_size=text_size)
        table.cell(mtf_table, 7, 4, str.tostring(supportLevel_30m, '#.##'), text_color=color.white, bgcolor=color.new(color.green, 60), text_size=text_size)
        table.cell(mtf_table, 8, 4, str.tostring(resistanceLevel_30m, '#.##'), text_color=color.white, bgcolor=color.new(color.red, 60), text_size=text_size)
        table.cell(mtf_table, 9, 4, str.tostring(dist_to_sup_30m, '#.##'), text_color=color.black, bgcolor=color.new(color.green, 80), text_size=text_size)
        table.cell(mtf_table, 10, 4, str.tostring(dist_to_res_30m, '#.##'), text_color=color.black, bgcolor=color.new(color.red, 80), text_size=text_size)
        table.cell(mtf_table, 11, 4, srbr_signal_30m, text_color=color.white, bgcolor=srbr_signal_color_30m, text_size=text_size)
        table.cell(mtf_table, 12, 4, combined_signal_30m, text_color=color.white, bgcolor=combined_signal_color_30m, text_size=text_size)
        table.cell(mtf_table, 13, 4, signal_strength_30m, text_color=color.white, bgcolor=combined_signal_color_30m, text_size=text_size)

    // ═════════ 45分钟时间框架数据（融合版本） ═════════
    if mtf_45m and not na(rsi_45m)
        // 计算SRBR距离
        [dist_to_sup_45m, dist_to_res_45m] = get_distance_to_levels_srbr(close_45m, supportLevel_45m, resistanceLevel_45m)

        // 获取各种状态和颜色
        rsi_status_45m = get_rsi_status(rsi_45m, long_S, short_S)
        rsi_color_45m = get_rsi_color(rsi_45m, long_S, short_S, rsi_neutral_45m, rsi_overbought_45m, rsi_oversold_45m)
        price_color_45m = get_price_color(close_45m, upband1_45m, loband1_45m, price_in_channel_45m, price_above_r1_45m, price_below_s1_45m)
        ma200_bg_color_45m = get_distance_bg_color(distance_45m, distance_threshold_1m)

        // MRC信号颜色
        mrc_signal_color_45m = get_signal_color(osc_45m, long_45m, short_45m)

        // SRBR信号颜色
        srbr_signal_color_45m = get_price_status_color_srbr(close_45m, supportLevel_45m, resistanceLevel_45m)

        // 综合信号颜色
        combined_signal_color_45m = get_combined_signal_color(combined_signal_45m)

        // 填充表格数据（14列）
        table.cell(mtf_table, 0, 5, "45分钟", text_color=color.black, bgcolor=color.new(color.gray, 80), text_size=text_size)
        table.cell(mtf_table, 1, 5, str.tostring(close_45m, '#.##'), text_color=color.black, bgcolor=color.white, text_size=text_size)
        table.cell(mtf_table, 2, 5, str.tostring(rsi_45m, '#.#'), text_color=color.black, bgcolor=rsi_color_45m, text_size=text_size)
        table.cell(mtf_table, 3, 5, rsi_status_45m, text_color=color.black, bgcolor=rsi_color_45m, text_size=text_size)
        table.cell(mtf_table, 4, 5, get_price_position(close_45m, upband1_45m, loband1_45m), text_color=color.black, bgcolor=price_color_45m, text_size=text_size)
        table.cell(mtf_table, 5, 5, str.tostring(distance_45m, '#.##'), text_color=color.black, bgcolor=ma200_bg_color_45m, text_size=text_size)
        table.cell(mtf_table, 6, 5, mrc_signal_45m, text_color=color.white, bgcolor=mrc_signal_color_45m, text_size=text_size)
        table.cell(mtf_table, 7, 5, str.tostring(supportLevel_45m, '#.##'), text_color=color.white, bgcolor=color.new(color.green, 60), text_size=text_size)
        table.cell(mtf_table, 8, 5, str.tostring(resistanceLevel_45m, '#.##'), text_color=color.white, bgcolor=color.new(color.red, 60), text_size=text_size)
        table.cell(mtf_table, 9, 5, str.tostring(dist_to_sup_45m, '#.##'), text_color=color.black, bgcolor=color.new(color.green, 80), text_size=text_size)
        table.cell(mtf_table, 10, 5, str.tostring(dist_to_res_45m, '#.##'), text_color=color.black, bgcolor=color.new(color.red, 80), text_size=text_size)
        table.cell(mtf_table, 11, 5, srbr_signal_45m, text_color=color.white, bgcolor=srbr_signal_color_45m, text_size=text_size)
        table.cell(mtf_table, 12, 5, combined_signal_45m, text_color=color.white, bgcolor=combined_signal_color_45m, text_size=text_size)
        table.cell(mtf_table, 13, 5, signal_strength_45m, text_color=color.white, bgcolor=combined_signal_color_45m, text_size=text_size)

    // ═════════ 1小时时间框架数据（融合版本） ═════════
    if mtf_1h and not na(rsi_1h)
        // 计算SRBR距离
        [dist_to_sup_1h, dist_to_res_1h] = get_distance_to_levels_srbr(close_1h, supportLevel_1h, resistanceLevel_1h)

        // 获取各种状态和颜色
        rsi_status_1h = get_rsi_status(rsi_1h, long_S, short_S)
        rsi_color_1h = get_rsi_color(rsi_1h, long_S, short_S, rsi_neutral_1h, rsi_overbought_1h, rsi_oversold_1h)
        price_color_1h = get_price_color(close_1h, upband1_1h, loband1_1h, price_in_channel_1h, price_above_r1_1h, price_below_s1_1h)
        ma200_bg_color_1h = get_distance_bg_color(distance_1h, distance_threshold_1m)

        // MRC信号颜色
        mrc_signal_color_1h = get_signal_color(osc_1h, long_1h, short_1h)

        // SRBR信号颜色
        srbr_signal_color_1h = get_price_status_color_srbr(close_1h, supportLevel_1h, resistanceLevel_1h)

        // 综合信号颜色
        combined_signal_color_1h = get_combined_signal_color(combined_signal_1h)

        // 填充表格数据（14列）
        table.cell(mtf_table, 0, 6, "1小时", text_color=color.black, bgcolor=color.new(color.gray, 80), text_size=text_size)
        table.cell(mtf_table, 1, 6, str.tostring(close_1h, '#.##'), text_color=color.black, bgcolor=color.white, text_size=text_size)
        table.cell(mtf_table, 2, 6, str.tostring(rsi_1h, '#.#'), text_color=color.black, bgcolor=rsi_color_1h, text_size=text_size)
        table.cell(mtf_table, 3, 6, rsi_status_1h, text_color=color.black, bgcolor=rsi_color_1h, text_size=text_size)
        table.cell(mtf_table, 4, 6, get_price_position(close_1h, upband1_1h, loband1_1h), text_color=color.black, bgcolor=price_color_1h, text_size=text_size)
        table.cell(mtf_table, 5, 6, str.tostring(distance_1h, '#.##'), text_color=color.black, bgcolor=ma200_bg_color_1h, text_size=text_size)
        table.cell(mtf_table, 6, 6, mrc_signal_1h, text_color=color.white, bgcolor=mrc_signal_color_1h, text_size=text_size)
        table.cell(mtf_table, 7, 6, str.tostring(supportLevel_1h, '#.##'), text_color=color.white, bgcolor=color.new(color.green, 60), text_size=text_size)
        table.cell(mtf_table, 8, 6, str.tostring(resistanceLevel_1h, '#.##'), text_color=color.white, bgcolor=color.new(color.red, 60), text_size=text_size)
        table.cell(mtf_table, 9, 6, str.tostring(dist_to_sup_1h, '#.##'), text_color=color.black, bgcolor=color.new(color.green, 80), text_size=text_size)
        table.cell(mtf_table, 10, 6, str.tostring(dist_to_res_1h, '#.##'), text_color=color.black, bgcolor=color.new(color.red, 80), text_size=text_size)
        table.cell(mtf_table, 11, 6, srbr_signal_1h, text_color=color.white, bgcolor=srbr_signal_color_1h, text_size=text_size)
        table.cell(mtf_table, 12, 6, combined_signal_1h, text_color=color.white, bgcolor=combined_signal_color_1h, text_size=text_size)
        table.cell(mtf_table, 13, 6, signal_strength_1h, text_color=color.white, bgcolor=combined_signal_color_1h, text_size=text_size)

    // ═════════ 4小时时间框架数据（融合版本） ═════════
    if mtf_4h and not na(rsi_4h)
        // 计算SRBR距离
        [dist_to_sup_4h, dist_to_res_4h] = get_distance_to_levels_srbr(close_4h, supportLevel_4h, resistanceLevel_4h)

        // 获取各种状态和颜色
        rsi_status_4h = get_rsi_status(rsi_4h, long_S, short_S)
        rsi_color_4h = get_rsi_color(rsi_4h, long_S, short_S, rsi_neutral_4h, rsi_overbought_4h, rsi_oversold_4h)
        price_color_4h = get_price_color(close_4h, upband1_4h, loband1_4h, price_in_channel_4h, price_above_r1_4h, price_below_s1_4h)
        ma200_bg_color_4h = get_distance_bg_color(distance_4h, distance_threshold_1m)

        // MRC信号颜色
        mrc_signal_color_4h = get_signal_color(osc_4h, long_4h, short_4h)

        // SRBR信号颜色
        srbr_signal_color_4h = get_price_status_color_srbr(close_4h, supportLevel_4h, resistanceLevel_4h)

        // 综合信号颜色
        combined_signal_color_4h = get_combined_signal_color(combined_signal_4h)

        // 填充表格数据（14列）
        table.cell(mtf_table, 0, 7, "4小时", text_color=color.black, bgcolor=color.new(color.gray, 80), text_size=text_size)
        table.cell(mtf_table, 1, 7, str.tostring(close_4h, '#.##'), text_color=color.black, bgcolor=color.white, text_size=text_size)
        table.cell(mtf_table, 2, 7, str.tostring(rsi_4h, '#.#'), text_color=color.black, bgcolor=rsi_color_4h, text_size=text_size)
        table.cell(mtf_table, 3, 7, rsi_status_4h, text_color=color.black, bgcolor=rsi_color_4h, text_size=text_size)
        table.cell(mtf_table, 4, 7, get_price_position(close_4h, upband1_4h, loband1_4h), text_color=color.black, bgcolor=price_color_4h, text_size=text_size)
        table.cell(mtf_table, 5, 7, str.tostring(distance_4h, '#.##'), text_color=color.black, bgcolor=ma200_bg_color_4h, text_size=text_size)
        table.cell(mtf_table, 6, 7, mrc_signal_4h, text_color=color.white, bgcolor=mrc_signal_color_4h, text_size=text_size)
        table.cell(mtf_table, 7, 7, str.tostring(supportLevel_4h, '#.##'), text_color=color.white, bgcolor=color.new(color.green, 60), text_size=text_size)
        table.cell(mtf_table, 8, 7, str.tostring(resistanceLevel_4h, '#.##'), text_color=color.white, bgcolor=color.new(color.red, 60), text_size=text_size)
        table.cell(mtf_table, 9, 7, str.tostring(dist_to_sup_4h, '#.##'), text_color=color.black, bgcolor=color.new(color.green, 80), text_size=text_size)
        table.cell(mtf_table, 10, 7, str.tostring(dist_to_res_4h, '#.##'), text_color=color.black, bgcolor=color.new(color.red, 80), text_size=text_size)
        table.cell(mtf_table, 11, 7, srbr_signal_4h, text_color=color.white, bgcolor=srbr_signal_color_4h, text_size=text_size)
        table.cell(mtf_table, 12, 7, combined_signal_4h, text_color=color.white, bgcolor=combined_signal_color_4h, text_size=text_size)
        table.cell(mtf_table, 13, 7, signal_strength_4h, text_color=color.white, bgcolor=combined_signal_color_4h, text_size=text_size)




// ═════════ RSI详细表格（左下角，可配置显示） ═════════

if show_rsi_table and barstate.islast
    var table rsi_table = table.new(
         position = position.bottom_left,  // 左下角
         columns = 2,
         rows = 10,
         bgcolor = color.new(color.white, 85),
         border_width = 1)

    table.clear(rsi_table, 0, 0, 1, 9)

    // 获取RSI表格的文字大小
    rsi_text_size = get_text_size()

    // RSI详细信息表格内容（预留）
    table.cell(rsi_table, 0, 0, "RSI详细分析", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=rsi_text_size)
    table.cell(rsi_table, 1, 0, "", text_color=color.black, text_size=rsi_text_size)

// ═════════ 高级警报状态表格（已禁用，整合到信息表格中） ═════════

if false  // 禁用独立的高级警报表格
    var table alert_status_table = table.new(
         position = position.top_right,
         columns = 2,
         rows = 12,
         bgcolor = color.new(color.white, 85),
         border_width = 1)

    table.clear(alert_status_table, 0, 0, 1, 11)

    text_size = get_text_size()

    // 标题
    table.cell(alert_status_table, 0, 0, "高级警报状态", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)
    table.cell(alert_status_table, 1, 0, "当前状态", text_color=color.white, bgcolor=color.new(color.purple, 30), text_size=text_size)

    // 启动条件状态
    long_trigger_status = long_trigger ? "✓ 已触发" : na(long_trigger_time) ? "等待中" : "监控中"
    short_trigger_status = short_trigger ? "✓ 已触发" : na(short_trigger_time) ? "等待中" : "监控中"

    table.cell(alert_status_table, 0, 1, "做多启动", text_color=color.black, text_size=text_size)
    table.cell(alert_status_table, 1, 1, long_trigger_status, text_color=color.black, bgcolor=long_trigger ? color.new(color.green, 60) : color.white, text_size=text_size)

    table.cell(alert_status_table, 0, 2, "做空启动", text_color=color.black, text_size=text_size)
    table.cell(alert_status_table, 1, 2, short_trigger_status, text_color=color.black, bgcolor=short_trigger ? color.new(color.red, 60) : color.white, text_size=text_size)

    // 时间窗口状态
    long_window_status = not na(long_trigger_time) ? (in_time_window(long_trigger_time, time_window_15min) ? "窗口内" : "已过期") : "未启动"
    short_window_status = not na(short_trigger_time) ? (in_time_window(short_trigger_time, time_window_15min) ? "窗口内" : "已过期") : "未启动"

    table.cell(alert_status_table, 0, 3, "做多窗口", text_color=color.black, text_size=text_size)
    table.cell(alert_status_table, 1, 3, long_window_status, text_color=color.black, text_size=text_size)

    table.cell(alert_status_table, 0, 4, "做空窗口", text_color=color.black, text_size=text_size)
    table.cell(alert_status_table, 1, 4, short_window_status, text_color=color.black, text_size=text_size)

    // 冷却状态
    long_cooldown_status = is_cooldown_over(last_long_alert_time, alert_cooldown_minutes) ? "可用" : "冷却中"
    short_cooldown_status = is_cooldown_over(last_short_alert_time, alert_cooldown_minutes) ? "可用" : "冷却中"

    table.cell(alert_status_table, 0, 5, "做多冷却", text_color=color.black, text_size=text_size)
    table.cell(alert_status_table, 1, 5, long_cooldown_status, text_color=color.black, text_size=text_size)

    table.cell(alert_status_table, 0, 6, "做空冷却", text_color=color.black, text_size=text_size)
    table.cell(alert_status_table, 1, 6, short_cooldown_status, text_color=color.black, text_size=text_size)

    // 条件状态检查
    condition2_1m_long = not na(long_trigger_time) and in_time_window(long_trigger_time, time_window_5min) and rsi_overbought_1m and price_above_r1_1m and ma200_far_1m and ma50_above_mean_1m
    condition3_5m_long = not na(long_trigger_time) and in_time_window(long_trigger_time, time_window_15min) and rsi_overbought_5m and price_above_r1_5m and ma200_far_5m and ma50_above_mean_1m
    condition4_15m_long = not na(long_trigger_time) and in_time_window(long_trigger_time, time_window_10min) and distance_to_s1_15m > get_r1s1_distance_threshold()

    condition2_1m_short = not na(short_trigger_time) and in_time_window(short_trigger_time, time_window_5min) and rsi_oversold_1m and price_below_s1_1m and ma200_far_1m and ma50_below_mean_1m
    condition3_5m_short = not na(short_trigger_time) and in_time_window(short_trigger_time, time_window_15min) and rsi_oversold_5m and price_below_s1_5m and ma200_far_5m and ma50_below_mean_1m
    condition4_15m_short = not na(short_trigger_time) and in_time_window(short_trigger_time, time_window_10min) and distance_to_r1_15m > get_r1s1_distance_threshold()

    // 条件状态显示
    table.cell(alert_status_table, 0, 7, "条件2(1m)", text_color=color.black, text_size=text_size)
    condition2_status = condition2_1m_long ? "✓做多" : condition2_1m_short ? "✓做空" : "未满足"
    condition2_color = condition2_1m_long ? color.new(color.green, 60) : condition2_1m_short ? color.new(color.red, 60) : color.white
    table.cell(alert_status_table, 1, 7, condition2_status, text_color=color.black, bgcolor=condition2_color, text_size=text_size)

    table.cell(alert_status_table, 0, 8, "条件3(5m)", text_color=color.black, text_size=text_size)
    condition3_status = condition3_5m_long ? "✓做多" : condition3_5m_short ? "✓做空" : "未满足"
    condition3_color = condition3_5m_long ? color.new(color.green, 60) : condition3_5m_short ? color.new(color.red, 60) : color.white
    table.cell(alert_status_table, 1, 8, condition3_status, text_color=color.black, bgcolor=condition3_color, text_size=text_size)

    table.cell(alert_status_table, 0, 9, "条件4(15m)", text_color=color.black, text_size=text_size)
    condition4_status = condition4_15m_long ? "✓做多" : condition4_15m_short ? "✓做空" : "未满足"
    condition4_color = condition4_15m_long ? color.new(color.green, 60) : condition4_15m_short ? color.new(color.red, 60) : color.white
    table.cell(alert_status_table, 1, 9, condition4_status, text_color=color.black, bgcolor=condition4_color, text_size=text_size)

    // 综合条件状态
    all_long_conditions = condition2_1m_long and condition3_5m_long and condition4_15m_long
    all_short_conditions = condition2_1m_short and condition3_5m_short and condition4_15m_short

    table.cell(alert_status_table, 0, 10, "综合条件", text_color=color.black, text_size=text_size)
    combined_status = all_long_conditions ? "✓做多就绪" : all_short_conditions ? "✓做空就绪" : "未就绪"
    combined_color = all_long_conditions ? color.new(color.lime, 60) : all_short_conditions ? color.new(color.fuchsia, 60) : color.white
    table.cell(alert_status_table, 1, 10, combined_status, text_color=color.black, bgcolor=combined_color, text_size=text_size)

    // 最终警报状态
    final_long_status = advanced_long_alert ? "🚀 警报!" : "等待中"
    final_short_status = advanced_short_alert ? "🔻 警报!" : "等待中"

    table.cell(alert_status_table, 0, 11, "警报状态", text_color=color.black, text_size=text_size)
    alert_status_text = advanced_long_alert ? final_long_status : advanced_short_alert ? final_short_status : "无警报"
    alert_status_color = advanced_long_alert ? color.new(color.lime, 40) : advanced_short_alert ? color.new(color.fuchsia, 40) : color.white
    table.cell(alert_status_table, 1, 11, alert_status_text, text_color=color.black, bgcolor=alert_status_color, text_size=text_size)

// ═════════ 实时价格标签显示信号结论 ═════════

// 确定当前主要信号（包含高级警报）
current_signal = advanced_long_alert ? "🚀高级做多" : advanced_short_alert ? "🔻高级做空" : long_signal ? "🚀做多" : short_signal ? "🔻做空" : oscillation_signal ? "⚡震荡" : "观望"
signal_color = advanced_long_alert ? color.lime : advanced_short_alert ? color.fuchsia : long_signal ? color.green : short_signal ? color.red : oscillation_signal ? color.orange : color.gray

// 创建唯一的实时价格标签（避免重复）
var label price_signal_label = na

// 在最后一根K线上显示实时价格标签
if barstate.islast
    // 删除之前的标签
    if not na(price_signal_label)
        label.delete(price_signal_label)

    // 价格标签功能已移除，只保留表格显示

//************************************************************************************************************
// RSI 窗口二显示
//************************************************************************************************************

// RSI 数据计算保留用于表格显示（移除plot输出）

// RSI 信息表格（可选显示，计算始终保留用于警报）
if show_rsi_table and barstate.islast
    // 创建RSI表格
    var table rsi_table = table.new(
         position = position.bottom_right,
         columns = 2,
         rows = 8,
         bgcolor = color.new(color.yellow, 80),
         border_width = 1)

    // 清空表格
    table.clear(rsi_table, 0, 0, 1, 7)

    // 添加RSI标题
    table.cell(rsi_table, 0, 0, "RSI指标", text_color=color.black, bgcolor=color.new(color.orange, 70), text_size=size.small)
    table.cell(rsi_table, 1, 0, "实时数值", text_color=color.black, bgcolor=color.new(color.orange, 70), text_size=size.small)

    // RSI 值
    table.cell(rsi_table, 0, 1, "RSI值", text_color=color.black, text_size=size.tiny)
    table.cell(rsi_table, 1, 1, str.tostring(rsi, '#.##'), text_color=color.black, text_size=size.tiny)

    // RSI 位置
    table.cell(rsi_table, 0, 2, "RSI位置", text_color=color.black, text_size=size.tiny)
    table.cell(rsi_table, 1, 2, rsi_position, text_color=color.black, text_size=size.tiny)

    // RSI 阈值
    table.cell(rsi_table, 0, 3, "超买阈值", text_color=color.black, text_size=size.tiny)
    table.cell(rsi_table, 1, 3, str.tostring(long_S, '#.##'), text_color=color.black, text_size=size.tiny)

    table.cell(rsi_table, 0, 4, "超卖阈值", text_color=color.black, text_size=size.tiny)
    table.cell(rsi_table, 1, 4, str.tostring(short_S, '#.##'), text_color=color.black, text_size=size.tiny)

    // RSI 穿越次数
    table.cell(rsi_table, 0, 5, "穿越次数", text_color=color.black, text_size=size.tiny)
    table.cell(rsi_table, 1, 5, str.tostring(rsi_cross_count), text_color=color.black, text_size=size.tiny)

    // RSI 穿越类型
    cross_type = segment_count > 0 ? "超买穿越" : segment_count < 0 ? "超卖穿越" : "无穿越"
    table.cell(rsi_table, 0, 6, "穿越类型", text_color=color.black, text_size=size.tiny)
    table.cell(rsi_table, 1, 6, cross_type, text_color=color.black, text_size=size.tiny)

    // RSI 状态标记
    table.cell(rsi_table, 0, 7, "状态标记", text_color=color.black, text_size=size.tiny)
    table.cell(rsi_table, 1, 7, str.tostring(segment_count), text_color=color.black, text_size=size.tiny)

//************************************************************************************************************
// 趋势判断逻辑
//************************************************************************************************************

// 综合趋势判断
trend_signal = ""
market_type = ""

// 删除不再使用的角度趋势判断（已在前面重新定义）

// 价格相对MA200位置
price_above_ma200 = close > ma200
price_below_ma200 = close < ma200

// RSI 趋势确认
rsi_bullish = rsi > 50 and rsi < long_S
rsi_bearish = rsi < 50 and rsi > short_S
rsi_overbought = rsi > long_S
rsi_oversold = rsi < short_S

// 重新设计的综合判断逻辑
// 首先判断是否为震荡
if is_oscillating
    trend_signal := "震荡行情"
    market_type := "震荡"
// 然后判断趋势强度和方向
else if ma200_trend_direction == "上升"
    if ma200_strength == "强" and price_above_ma200 and (rsi_bullish or rsi_overbought)
        trend_signal := "强多头趋势"
        market_type := "强趋势"
    else if (ma200_strength == "中" or ma200_strength == "弱") and price_above_ma200
        trend_signal := "弱多头趋势"
        market_type := "弱趋势"
    else
        trend_signal := "多头整理"
        market_type := "整理"
else if ma200_trend_direction == "下降"
    if ma200_strength == "强" and price_below_ma200 and (rsi_bearish or rsi_oversold)
        trend_signal := "强空头趋势"
        market_type := "强趋势"
    else if (ma200_strength == "中" or ma200_strength == "弱") and price_below_ma200
        trend_signal := "弱空头趋势"
        market_type := "弱趋势"
    else
        trend_signal := "空头整理"
        market_type := "整理"
else  // 横盘
    trend_signal := "横盘整理"
    market_type := "整理"

// 在图表上显示趋势判断
if barstate.islast and show_info_table and not enable_strategy
    // 创建趋势判断表格（只在策略未启用时显示，避免位置冲突）
    var table trend_table = table.new(
         position = position.top_left,
         columns = 2,
         rows = 4,
         bgcolor = color.new(color.green, 80),
         border_width = 2)

    // 清空表格
    table.clear(trend_table, 0, 0, 1, 3)

    // 添加趋势标题
    table.cell(trend_table, 0, 0, "市场分析", text_color=color.white, bgcolor=color.new(color.purple, 50), text_size=size.normal)
    table.cell(trend_table, 1, 0, "判断结果", text_color=color.white, bgcolor=color.new(color.purple, 50), text_size=size.normal)

    // 趋势方向
    trend_color = trend_signal == "多头趋势" ? color.green : trend_signal == "空头趋势" ? color.red : color.orange
    table.cell(trend_table, 0, 1, "趋势方向", text_color=color.black, text_size=size.small)
    table.cell(trend_table, 1, 1, trend_signal, text_color=color.white, bgcolor=trend_color, text_size=size.small)

    // 市场类型
    table.cell(trend_table, 0, 2, "市场类型", text_color=color.black, text_size=size.small)
    table.cell(trend_table, 1, 2, market_type, text_color=color.black, text_size=size.small)

    // MA200趋势
    table.cell(trend_table, 0, 3, "MA200趋势", text_color=color.black, text_size=size.small)
    table.cell(trend_table, 1, 3, ma200_trend_direction, text_color=color.black, text_size=size.small)

//************************************************************************************************************
// RSI 分段线显示（已禁用 - 使用独立的RSI窗口）
//************************************************************************************************************

// 注释：由于使用了独立的RSI窗口2.c，这里不再绘制RSI相关的线条和标签
// 避免在主图表K线0附近出现橙黄色横条

// RSI 状态变化时绘制分段线（已禁用）
var int prev_rsi_state = 0

// 禁用RSI分段线绘制，因为有独立的RSI窗口
if false  // 原条件：current_state != rsi_state and not na(rsi)
    // 确定分段线颜色
    segment_color = color.black

    if prev_rsi_state == 0 and current_state == 1
        segment_color := color.new(color.red, 0)  // 从中性区进入超买区
    else if prev_rsi_state == 0 and current_state == -1
        segment_color := color.new(color.blue, 0)  // 从中性区进入超卖区
    else if prev_rsi_state == 1 and current_state == 0
        segment_color := color.new(color.orange, 0)  // 从超买区进入中性区
    else if prev_rsi_state == -1 and current_state == 0
        segment_color := color.new(color.green, 0)  // 从超卖区进入中性区
    else if prev_rsi_state == 1 and current_state == -1
        segment_color := color.new(color.purple, 0)  // 从超买区直接到超卖区
    else if prev_rsi_state == -1 and current_state == 1
        segment_color := color.new(color.yellow, 0)  // 从超卖区直接到超买区
    else if prev_rsi_state == 1 and current_state == 1
        segment_color := color.new(color.maroon, 0)  // 超买区内的重复进入
    else if prev_rsi_state == -1 and current_state == -1
        segment_color := color.new(color.navy, 0)  // 超卖区内的重复进入

    // 在RSI窗口绘制垂直分段线（已禁用）
    // line.new(bar_index, 0, bar_index, 100,
    //          color = segment_color,
    //          width = 2,
    //          style = line.style_solid,
    //          extend = extend.none)

    // RSI计数标签已移除

    // 更新前一个状态
    prev_rsi_state := rsi_state

//************************************************************************************************************
// 价格格式化函数
//************************************************************************************************************

format_price(price) =>
    str.tostring(price, '#.####')

//************************************************************************************************************
// 价格标签显示（参考MRC原始实现）
//************************************************************************************************************

// 声明标签变量
var label mean_label = na
var label res1_label = na
var label sup1_label = na
var label res2_label = na
var label sup2_label = na
var label lbl50 = na
var label lbl100 = na
var label lbl200 = na

// 所有价格线标签已移除，只保留多时间周期表格显示

//************************************************************************************************************
// 策略交易逻辑
//************************************************************************************************************

// ═════════ 策略状态管理（变量已在前面定义） ═════════

// ═════════ 仓位管理函数 ═════════
// 检查是否可以开新仓
can_open_position() =>
    if not enable_strategy
        false
    else
        // 基本仓位检查
        current_positions = strategy.position_size != 0 ? 1 : 0
        positions_ok = current_positions < max_concurrent_positions

        // 每日交易次数检查
        trades_ok = daily_trade_count < max_daily_trades

        // 每日亏损检查
        loss_ok = daily_pnl > -max_daily_loss_percent

        // 时间过滤检查
        time_ok = true
        if enable_time_filter
            current_hour = hour(time)
            time_ok := current_hour >= start_hour and current_hour <= end_hour

        positions_ok and trades_ok and loss_ok and time_ok

// ═════════ 信号质量检查函数 ═════════
// 检查信号是否满足高级过滤条件
is_signal_quality_good(signal_direction) =>
    if not enable_strategy
        false
    else
        quality_ok = true

        // 信号强度检查
        if require_strong_signals
            current_strength = signal_strength_1m
            quality_ok := quality_ok and current_strength == "强"

        // 趋势过滤检查
        if enable_trend_filter
            trend_ok = false
            if signal_direction == "LONG"
                trend_ok := trend_signal == "上涨趋势" or trend_signal == "震荡上涨"
            else if signal_direction == "SHORT"
                trend_ok := trend_signal == "下跌趋势" or trend_signal == "震荡下跌"
            quality_ok := quality_ok and trend_ok

        // 通道宽度检查
        current_channel_width = upband1 - loband1
        width_ok = current_channel_width >= min_channel_width
        quality_ok := quality_ok and width_ok

        // 成交量过滤检查
        if enable_volume_filter_strategy and enable_volume_filter
            volume_ok = false
            if signal_direction == "LONG"
                volume_ok := long_volume_ok
            else if signal_direction == "SHORT"
                volume_ok := short_volume_ok
            quality_ok := quality_ok and volume_ok

        // RSI过滤检查
        if enable_rsi_filter
            rsi_ok = true
            if signal_direction == "LONG"
                rsi_ok := rsi_1m < rsi_overbought_threshold
            else if signal_direction == "SHORT"
                rsi_ok := rsi_1m > rsi_oversold_threshold
            quality_ok := quality_ok and rsi_ok

        // MA距离过滤检查
        if enable_ma_distance_filter
            ma_distance_percent = math.abs(close - ma200) / ma200 * 100
            distance_ok = ma_distance_percent <= max_ma_distance_percent
            quality_ok := quality_ok and distance_ok

        quality_ok

// ═════════ JSON通知函数（已在前面定义） ═════════

// ═════════ 每日统计重置（全局作用域） ═════════
current_day = dayofweek(time)
if na(last_trade_day) or last_trade_day != current_day
    daily_trade_count := 0
    daily_pnl := 0.0
    last_trade_day := current_day

// ═════════ 主策略逻辑 ═════════
if enable_strategy
    // 检测当前信号
    [signal_long, signal_short, signal_direction] = detect_1m_signal()

    // 检查是否有新信号
    has_new_signal = not na(signal_direction)

    // 信号过期检查
    signal_expired = is_signal_expired()

    // 反向信号检查
    reverse_signal = false
    if not na(current_position_direction) and not na(signal_direction)
        reverse_signal := (current_position_direction == "LONG" and signal_direction == "SHORT") or (current_position_direction == "SHORT" and signal_direction == "LONG")

    // ═════════ 开仓逻辑 ═════════
    if has_new_signal and can_open_position() and not position_opened
        if signal_long and is_signal_quality_good("LONG")
            qty = position_size_percent / 100 * strategy.equity / close
            strategy.entry("Long", strategy.long, qty=qty, comment="综合做多信号")
            position_opened := true
            entry_price := close
            entry_time := time
            current_position_direction := "LONG"
            last_signal_time := time
            last_signal_direction := "LONG"
            daily_trade_count := daily_trade_count + 1

            // JSON通知
            if enable_json_alerts
                alert(generate_json_alert("OPEN", "LONG", close, "综合做多信号触发"), alert.freq_once_per_bar)

        else if signal_short and is_signal_quality_good("SHORT")
            qty = position_size_percent / 100 * strategy.equity / close
            strategy.entry("Short", strategy.short, qty=qty, comment="综合做空信号")
            position_opened := true
            entry_price := close
            entry_time := time
            current_position_direction := "SHORT"
            last_signal_time := time
            last_signal_direction := "SHORT"
            daily_trade_count := daily_trade_count + 1

            // JSON通知
            if enable_json_alerts
                alert(generate_json_alert("OPEN", "SHORT", close, "综合做空信号触发"), alert.freq_once_per_bar)

    // ═════════ 信号延续逻辑 ═════════
    // 如果有新的同方向信号，更新最后信号时间
    if has_new_signal and position_opened and not na(last_signal_direction)
        if signal_direction == last_signal_direction
            last_signal_time := time

            // JSON通知 - 信号延续
            if enable_json_alerts
                alert(generate_json_alert("HOLD", signal_direction, close, "同方向信号延续"), alert.freq_once_per_bar)

    // ═════════ 平仓逻辑 ═════════
    close_position = false
    close_reason = ""

    // 1. 信号过期平仓
    if position_opened and signal_expired
        close_position := true
        close_reason := "信号过期(" + str.tostring(signal_hold_minutes) + "分钟无新信号)"

    // 2. 反向信号平仓
    if position_opened and enable_reverse_signals and reverse_signal
        close_position := true
        close_reason := "反向信号触发"

    // 3. 止损平仓
    if position_opened and enable_stop_loss and not na(entry_price)
        if current_position_direction == "LONG" and close <= entry_price * (1 - stop_loss_percent/100)
            close_position := true
            close_reason := "止损触发(-" + str.tostring(stop_loss_percent) + "%)"
        else if current_position_direction == "SHORT" and close >= entry_price * (1 + stop_loss_percent/100)
            close_position := true
            close_reason := "止损触发(-" + str.tostring(stop_loss_percent) + "%)"

    // 4. 止盈平仓
    if position_opened and enable_take_profit and not na(entry_price)
        if current_position_direction == "LONG" and close >= entry_price * (1 + take_profit_percent/100)
            close_position := true
            close_reason := "止盈触发(+" + str.tostring(take_profit_percent) + "%)"
        else if current_position_direction == "SHORT" and close <= entry_price * (1 - take_profit_percent/100)
            close_position := true
            close_reason := "止盈触发(+" + str.tostring(take_profit_percent) + "%)"

    // 执行平仓
    if close_position
        strategy.close_all(comment=close_reason)

        // 计算本次交易盈亏
        if not na(entry_price)
            trade_pnl = (close - entry_price) / entry_price * 100 * (current_position_direction == "LONG" ? 1 : -1)
            daily_pnl := daily_pnl + trade_pnl

        // JSON通知
        if enable_json_alerts
            pnl_info = not na(entry_price) ? ", \"pnl\": " + str.tostring(trade_pnl, '#.2') + "%" : ""
            enhanced_json = '{"action":"CLOSE","strategy":"MRC+MA200+RSI+SRBR","symbol":"' + syminfo.ticker + '","timeframe":"' + timeframe.period + '","price":"' + str.tostring(close, '#.####') + '","direction":"' + current_position_direction + '","entry_price":"' + str.tostring(entry_price, '#.####') + '","time":"' + str.format_time(time, "yyyy-MM-dd HH:mm:ss") + '","reason":"' + close_reason + '"' + pnl_info + '}'
            alert(enhanced_json, alert.freq_once_per_bar)

        // 重置状态
        position_opened := false
        entry_price := na
        entry_time := na
        current_position_direction := na
        last_signal_time := na
        last_signal_direction := na

//************************************************************************************************************
// 策略图表标记和绘图
//************************************************************************************************************

// ═════════ 策略信号标记 ═════════
// 获取当前信号状态
[current_signal_long, current_signal_short, current_signal_direction] = detect_1m_signal()

// 策略开仓标记
plotshape(enable_strategy and current_signal_long and not current_signal_long[1], title="策略做多信号", style=shape.triangleup, location=location.belowbar, color=color.new(color.lime, 0), size=size.normal, text="策略多", textcolor=color.white)
plotshape(enable_strategy and current_signal_short and not current_signal_short[1], title="策略做空信号", style=shape.triangledown, location=location.abovebar, color=color.new(color.red, 0), size=size.normal, text="策略空", textcolor=color.white)

// 策略持仓状态标记
plotshape(enable_strategy and position_opened and barstate.isconfirmed, title="策略持仓中", style=shape.circle, location=location.absolute, color=color.yellow, size=size.tiny)

// 策略平仓标记
plotshape(enable_strategy and not position_opened and not na(entry_price[1]) and barstate.isconfirmed, title="策略平仓", style=shape.xcross, location=location.absolute, color=color.orange, size=size.small)

// ═════════ 关键价位线绘制 ═════════
// MRC 通道线
plot(v_meanline, title="MEAN中线", color=color.new(color.blue, 0), linewidth=2, display=display.all)
plot(upband1, title="R1阻力线", color=color.new(color.red, 20), linewidth=1, display=display.all)
plot(loband1, title="S1支撑线", color=color.new(color.green, 20), linewidth=1, display=display.all)

// MA 线条
ma50_color = ma50 > ma50[1] ? color.new(color.orange, 30) : color.new(color.orange, 70)
ma200_color = ma200 > ma200[1] ? color.new(color.purple, 30) : color.new(color.purple, 70)
plot(ma50, title="MA50", color=ma50_color, linewidth=1, display=display.all)
plot(ma200, title="MA200", color=ma200_color, linewidth=2, display=display.all)

// ═════════ 策略入场价格线 ═════════
// 绘制入场价格水平线
var line entry_line = na
if enable_strategy and position_opened and not na(entry_price)
    if na(entry_line)
        entry_line := line.new(x1=bar_index, y1=entry_price, x2=bar_index+1, y2=entry_price, color=current_position_direction == "LONG" ? color.lime : color.red, width=2, style=line.style_dashed, extend=extend.right)
        line.set_xy1(entry_line, bar_index, entry_price)
        line.set_xy2(entry_line, bar_index+50, entry_price)
    else
        line.set_xy2(entry_line, bar_index, entry_price)
else if not position_opened and not na(entry_line)
    line.delete(entry_line)
    entry_line := na

// ═════════ 止损止盈线 ═════════
var line sl_line = na
var line tp_line = na

if enable_strategy and position_opened and not na(entry_price)
    // 止损线
    if enable_stop_loss
        sl_price = current_position_direction == "LONG" ? entry_price * (1 - stop_loss_percent/100) : entry_price * (1 + stop_loss_percent/100)
        if na(sl_line)
            sl_line := line.new(x1=bar_index, y1=sl_price, x2=bar_index+50, y2=sl_price, color=color.red, width=1, style=line.style_dotted, extend=extend.right)
        else
            line.set_xy1(sl_line, bar_index, sl_price)
            line.set_xy2(sl_line, bar_index+50, sl_price)

    // 止盈线
    if enable_take_profit
        tp_price = current_position_direction == "LONG" ? entry_price * (1 + take_profit_percent/100) : entry_price * (1 - take_profit_percent/100)
        if na(tp_line)
            tp_line := line.new(x1=bar_index, y1=tp_price, x2=bar_index+50, y2=tp_price, color=color.green, width=1, style=line.style_dotted, extend=extend.right)
        else
            line.set_xy1(tp_line, bar_index, tp_price)
            line.set_xy2(tp_line, bar_index+50, tp_price)
else
    // 清理线条
    if not na(sl_line)
        line.delete(sl_line)
        sl_line := na
    if not na(tp_line)
        line.delete(tp_line)
        tp_line := na

// ═════════ 背景颜色 ═════════
// 策略持仓背景色
strategy_bg_color = enable_strategy and position_opened ? (current_position_direction == "LONG" ? color.new(color.green, 95) : color.new(color.red, 95)) : na
bgcolor(strategy_bg_color, title="策略持仓背景")

//************************************************************************************************************
// 功能对比说明（注释）
//************************************************************************************************************

// ═════════ 原始功能保留对比 ═════════
//
// MRC 指标功能：
// ✓ SuperSmoother 和其他滤波器类型支持
// ✓ MEAN 线计算和显示
// ✓ R1/S1 通道计算和显示
// ✓ 价格标签显示
// ✓ 多时间框架支持（简化版）
// ✓ 所有原始参数设置
//
// MA200 指标功能：
// ✓ MA50/MA100/MA200 计算
// ✓ EMA/SMA 选择
// ✓ MA 线条颜色逻辑
// ✓ 价格标签显示
// ✓ 距离计算
// ✓ 斜率计算
//
// RSI 指标功能：
// ✓ 机器学习动态阈值
// ✓ RSI 平滑处理
// ✓ 多种移动平均类型
// ✓ 分段计数逻辑
// ✓ 穿越次数统计
// ✓ 状态变化检测
// ✓ 分段线颜色编码
//
// 新增综合功能：
// ✓ 三指标统一显示
// ✓ 实时数值表格
// ✓ 趋势综合判断
// ✓ 震荡/趋势识别
// ✓ 窗口一：K线+MRC+MA
// ✓ 窗口二：RSI独立显示
// ✓ 表格化数值显示
// ✓ 1-5级成交量过滤系统（参考SRBR源代码逻辑）
//   - 1级：最温和，只过滤极明显假信号
//   - 2级：温和，过滤明显假信号
//   - 3级：中等，标准过滤强度
//   - 4级：严格，较强过滤
//   - 5级：最严格，参考SRBR高低成交量阈值
//
// 新增策略功能：
// ✓ 基于1分钟综合信号的自动交易
// ✓ 信号持有时间管理（3分钟默认）
// ✓ 同方向信号延续机制
// ✓ 反向信号平仓机制
// ✓ 止损止盈功能
// ✓ JSON格式交易通知
// ✓ 仓位大小管理
// ✓ 最大并发持仓控制