# MRC+MA200+RSI+SRBR 策略说明

## 概述

本策略基于原始的 `4.pine` 指标文件改写而成，将综合技术指标信号转换为自动交易策略。策略主要基于1分钟时间框架的综合信号进行交易决策。

## 核心功能

### 1. 信号检测机制
- **基础信号**: 结合MRC、MA200、RSI和SRBR四个技术指标
- **综合判断**: 只有当MRC信号和SRBR信号方向一致时才产生交易信号
- **时间框架**: 主要使用1分钟时间框架的信号进行交易决策

### 2. 交易逻辑

#### 开仓条件
- 当1分钟时间周期出现综合信号警报时，下对应方向的订单
- 做多条件：MRC做多信号 + SRBR看多信号
- 做空条件：MRC做空信号 + SRBR看空信号

#### 持仓管理
- **信号延续机制**: 随后产生的每条同方向综合警报作为继续持有订单的依据
- **时间管理**: 默认3分钟内如果没有新的同方向信号，则平仓
- **信号更新**: 如果3分钟内继续出现新的同方向警报，则继续持有并重置计时

#### 平仓条件
1. **信号过期**: 3分钟内无新的同方向信号
2. **反向信号**: 出现反向综合信号时立即平仓
3. **止损**: 可选的百分比止损
4. **止盈**: 可选的百分比止盈

### 3. 风险管理
- **仓位控制**: 可设置仓位大小百分比
- **并发限制**: 最大并发持仓数控制
- **每日限制**: 每日最大交易次数和最大亏损限制
- **时间过滤**: 可设置交易时间窗口

## 参数配置

### 策略交易配置
- `enable_strategy`: 启用策略交易 (默认: true)
- `signal_hold_minutes`: 信号持有时间分钟数 (默认: 3)
- `position_size_percent`: 仓位大小百分比 (默认: 10%)
- `enable_stop_loss`: 启用止损 (默认: false)
- `stop_loss_percent`: 止损百分比 (默认: 2%)
- `enable_take_profit`: 启用止盈 (默认: false)
- `take_profit_percent`: 止盈百分比 (默认: 4%)
- `max_concurrent_positions`: 最大并发持仓数 (默认: 1)
- `enable_reverse_signals`: 启用反向信号平仓 (默认: true)

### 信号检测配置
- `use_1m_signals`: 使用1分钟综合信号 (默认: true)
- `require_volume_confirmation`: 要求成交量确认 (默认: true)
- `min_signal_strength`: 最小信号强度 (默认: "强")

### 风险管理配置
- `max_daily_trades`: 每日最大交易次数 (默认: 10)
- `max_daily_loss_percent`: 每日最大亏损百分比 (默认: 5%)
- `enable_time_filter`: 启用时间过滤 (默认: false)
- `start_hour`: 交易开始时间 (默认: 9)
- `end_hour`: 交易结束时间 (默认: 17)

## JSON通知格式

策略会生成JSON格式的交易通知，便于与外部系统集成：

### 开仓通知
```json
{
  "action": "OPEN",
  "direction": "LONG/SHORT",
  "symbol": "交易对",
  "timeframe": "时间框架",
  "price": "开仓价格",
  "position_size": "仓位大小%",
  "time": "时间戳",
  "reason": "综合信号触发",
  "strategy": "MRC+MA200+RSI+SRBR"
}
```

### 平仓通知
```json
{
  "action": "CLOSE",
  "direction": "LONG/SHORT",
  "symbol": "交易对",
  "timeframe": "时间框架", 
  "price": "平仓价格",
  "time": "时间戳",
  "reason": "平仓原因",
  "strategy": "MRC+MA200+RSI+SRBR"
}
```

### 信号延续通知
```json
{
  "action": "HOLD",
  "direction": "LONG/SHORT",
  "symbol": "交易对",
  "timeframe": "时间框架",
  "price": "当前价格",
  "time": "时间戳",
  "reason": "同方向信号延续",
  "strategy": "MRC+MA200+RSI+SRBR"
}
```

## 策略状态显示

策略在图表左上角显示实时状态表格，包含：
- 策略启用状态
- 当前持仓状态
- 持仓方向
- 入场价格
- 当前盈亏
- 信号剩余时间
- 当前信号状态
- 今日交易次数
- 今日盈亏

## 图表标记

- 🔺 绿色三角形：综合做多信号
- 🔻 红色三角形：综合做空信号
- ⚪ 黄色圆点：开仓标记
- ✖️ 橙色叉号：平仓标记

## 技术指标线条

- 蓝色线：MEAN中线
- 红色线：R1阻力线
- 绿色线：S1支撑线
- 橙色线：MA50
- 紫色线：MA200

## 使用建议

1. **回测验证**: 在实盘使用前，建议先进行充分的历史回测
2. **参数优化**: 根据不同品种和市场环境调整参数
3. **风险控制**: 合理设置仓位大小和风险管理参数
4. **监控运行**: 密切关注策略运行状态和JSON通知
5. **定期评估**: 定期评估策略表现并进行必要调整

## 注意事项

1. 策略基于技术指标，无法预测所有市场情况
2. 建议结合基本面分析和市场环境判断
3. 严格执行风险管理规则
4. 定期检查和更新策略参数
5. 保持对市场变化的敏感性

## 完成的功能清单

### ✅ 核心策略功能
- [x] 基于1分钟综合信号的自动交易
- [x] 首次信号下单，后续信号作为持仓依据
- [x] 3分钟信号延续机制（可配置）
- [x] 信号过期自动平仓
- [x] 反向信号立即平仓
- [x] JSON格式交易通知推送

### ✅ 高级策略功能
- [x] 信号强度过滤（仅使用"强"信号）
- [x] 趋势方向过滤
- [x] MRC通道宽度过滤
- [x] 成交量确认过滤
- [x] RSI极值区域过滤
- [x] MA200距离过滤
- [x] 可选止损止盈功能

### ✅ 风险管理系统
- [x] 仓位大小控制
- [x] 最大并发持仓限制
- [x] 每日交易次数限制
- [x] 每日最大亏损限制
- [x] 交易时间窗口过滤
- [x] 实时风险监控

### ✅ 用户界面功能
- [x] 策略状态实时表格（左上角）
- [x] 策略性能统计表格（右上角）
- [x] 综合信息表格（右下角，包含策略状态）
- [x] 多时间框架分析表格
- [x] 图表标记和信号显示
- [x] 入场价格线、止损止盈线

### ✅ 警报和通知系统
- [x] 策略开仓JSON警报
- [x] 策略平仓JSON警报
- [x] 信号延续JSON警报
- [x] 风险警报（交易次数、亏损限制）
- [x] 详细的盈亏信息

### ✅ 图表显示功能
- [x] MRC通道线（MEAN、R1、S1）
- [x] MA50/MA200移动平均线
- [x] 策略信号标记
- [x] 持仓状态标记
- [x] 入场价格水平线
- [x] 止损止盈参考线
- [x] 持仓背景色

## 文件说明

- `4.pine`: **完整策略版本**（推荐使用）
  - 包含所有原始指标功能
  - 完整的策略交易逻辑
  - 多时间框架分析
  - 详细的状态显示表格
  - 高级过滤和风险管理
  - 适合专业交易和深度分析

- `4_strategy.pine`: 简化策略版本
  - 核心交易逻辑
  - 基础功能实现
  - 适合学习和理解

- `策略说明.md`: 详细使用说明文档

## 推荐使用方式

**建议使用 `4.pine` 文件**，因为它：
1. 功能最完整，包含所有高级特性
2. 保留了原始指标的所有分析功能
3. 提供了最详细的状态监控
4. 具有最完善的风险管理系统
5. 适合实盘交易使用
